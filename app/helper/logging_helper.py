"""
Logging helper utilities for structured ASCII table logging.

This module provides universal functions to log key-value pairs in a formatted ASCII table
structure and create bordered text messages, making logs more readable and organized with
automatic truncation.
"""

import json
from typing import Any, Dict
from logging import Logger


def log_structured_data(
    logger: Logger,
    heading: str,
    data: Dict[str, Any],
    max_value_length: int = 100,
    level: str = "info",
) -> None:
    """
    Universal function to log key-value pairs in a formatted ASCII table structure.

    Args:
        logger: Logger instance to use for logging
        heading: Table heading to display
        data: Dictionary of key-value pairs to log
        max_value_length: Maximum length for value display before truncation
        level: Log level to use ('info', 'debug', 'warning', 'error')
    """
    if not data:
        return

    def truncate_string(value: str, max_len: int) -> str:
        """Truncate a string to a maximum length and add ellipsis if needed."""
        if not isinstance(value, str):
            value = str(value)

        if len(value) <= max_len:
            return value

        return value[:max_len] + "..."

    def format_value_for_display(value: Any, max_len: int) -> str:
        """Format a value for display in log table with truncation."""
        if value is None:
            return "null"
        elif isinstance(value, bool):
            return "true" if value else "false"
        elif isinstance(value, (dict, list)):
            # Convert complex objects to JSON string then truncate
            json_str = json.dumps(value, ensure_ascii=False, separators=(",", ":"))
            return truncate_string(json_str, max_len)
        else:
            return truncate_string(str(value), max_len)

    # Calculate column widths
    # Get all formatted values first to ensure consistent ordering
    formatted_items = []
    for key in data.keys():
        formatted_items.append(
            {
                "key": str(key),
                "value": format_value_for_display(data[key], max_value_length),
            }
        )

    max_key_width = max(len(item["key"]) for item in formatted_items)
    max_key_width = max(max_key_width, len("Key"))  # Ensure header fits

    max_value_width = max(len(item["value"]) for item in formatted_items)
    max_value_width = max(max_value_width, len("Value"))  # Ensure header fits

    # Table formatting
    total_width = (
        max_key_width + max_value_width + 7
    )  # +7 for " | ", " | ", and outer "|"
    border = "+-" + "-" * (total_width - 4) + "-+"
    header_separator = "+-" + "-" * max_key_width + "-+-" + "-" * max_value_width + "-+"

    # Build table
    table_lines = [
        border,
        f"| {heading:^{total_width - 4}} |",
        header_separator,
        f"| {' Key':<{max_key_width}} | {'Value':<{max_value_width}} |",
        header_separator,
    ]

    # Add data rows (use pre-formatted items to maintain order)
    for item in formatted_items:
        formatted_key = item["key"][:max_key_width].ljust(max_key_width)
        formatted_value = item["value"][:max_value_width].ljust(max_value_width)
        table_lines.append(f"| {formatted_key} | {formatted_value} |")

    table_lines.append(border)

    # Log each line separately to maintain correct order in OpenTelemetry systems like SigNoz
    # Logging as a single multi-line string can cause reversed order in some telemetry systems
    import sys

    # Get the log method based on level
    log_method = getattr(logger, level.lower(), logger.info)

    # Log each line separately to preserve order
    for line in table_lines:
        log_method(line)


def log_with_border(
    logger: Logger,
    message: str,
    border_char: str = "=",
    padding: int = 2,
    level: str = "info",
) -> None:
    """
    Log a message with a decorative border around it.

    Args:
        logger: Logger instance to use for logging
        message: Message to display (can be single or multi-line)
        border_char: Character to use for the border (default: "=")
        padding: Number of spaces to pad on each side of the message (default: 2)
        level: Log level to use ('info', 'debug', 'warning', 'error')

    Example:
        log_with_border(logger, "Service Started", border_char="=", padding=2)

        Output:
        ========================
          Service Started
        ========================
    """
    # Split message into lines if it contains newlines
    lines = message.split("\n")

    # Calculate the maximum line length
    max_length = max(len(line) for line in lines)

    # Add padding to width
    total_width = max_length + (padding * 2)

    # Create the border
    border = border_char * total_width

    # Build the bordered message
    bordered_lines = [border]

    for line in lines:
        # Center each line with padding
        padded_line = line.center(max_length)
        bordered_line = " " * padding + padded_line + " " * padding
        bordered_lines.append(bordered_line)

    bordered_lines.append(border)

    # Log each line separately to maintain correct order in OpenTelemetry systems like SigNoz
    # Logging as a single multi-line string can cause reversed order in some telemetry systems
    import sys

    # Get the log method based on level
    log_method = getattr(logger, level.lower(), logger.info)

    # Log each line separately to preserve order
    for line in bordered_lines:
        log_method(line)
