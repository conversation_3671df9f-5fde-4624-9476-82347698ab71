import json
import logging
import os
import time
import uuid
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import requests
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct
from requests.adapters import HTT<PERSON>dapter
from urllib3.util.retry import Retry

# Configure logging
update_log_file = os.path.join(os.path.dirname(__file__), "update.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(update_log_file)],
)
logger = logging.getLogger(__name__)

metadata_file = "update_metadata.json"
metadata_file = os.path.join(os.path.dirname(__file__), metadata_file)
if os.path.exists(metadata_file):
    with open(metadata_file, "r") as f:
        run_data = json.load(f)
else:
    run_data = {"last_run": None}

current_run = datetime.now().isoformat()


@dataclass
class Config:
    """Configuration class for API endpoints and settings"""
    BASE_URL: str = os.environ.get("API_GATEWAY_URL")

    PAGE_SIZE: int = 30
    REQUEST_TIMEOUT: int = 60
    MAX_RETRIES: int = 3
    BACKOFF_FACTOR: float = 0.3


config = Config()


QDRANT_HOST = os.environ.get("QDRANT_HOST")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
QDRANT_COLLECTION_NAME = os.environ.get("QDRANT_COLLECTION_NAME")

# Initialize Qdrant client
try:
    logger.info("Initializing Qdrant client...")
    client = QdrantClient(
        host=QDRANT_HOST,
        port=443,
        api_key=QDRANT_API_KEY,
    )
    logger.info("Qdrant client initialized successfully")

    # Get all existing point IDs
    final_ids = []
    collection_name = QDRANT_COLLECTION_NAME
    next_setoff = None
    while True:
        points, next_setoff = client.scroll(
            collection_name=collection_name,
            limit=1000,
            offset=next_setoff,
            with_payload=False,
            with_vectors=False,
        )
        final_ids.extend([point.id for point in points])
        logger.info(f"Found {len(final_ids)} existing points in collection")
        logger.info(f"Next setoff: {next_setoff}")
        if next_setoff is None:
            break
    logger.info(f"Found {len(final_ids)} existing points in collection")
except Exception as e:
    logger.error(f"Failed to initialize Qdrant client: {e}")



# Configure requests session with retry strategy
def create_session() -> requests.Session:
    """Create a requests session with retry strategy and timeout"""
    session = requests.Session()

    retry_strategy = Retry(
        total=config.MAX_RETRIES,
        backoff_factor=config.BACKOFF_FACTOR,
        status_forcelist=[429, 500, 502, 503, 504],
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    return session


session = create_session()


def get_embedding(data: Dict[str, Any]) -> List[float]:
    """Generate embedding for the given data"""
    try:
        # print(data)
        sent = f"Name : {data['name']}\nDescription : {data['description']}"
        if data["type"] == "mcp_tool":
            sent += f"\nMCP Name : {data['mcp_name']}"
        if data.get("category") is not None:
            sent += f"\nCategory : {data['category']}"
        else:
            sent += f"\nCategory : general"

        logger.debug(f"Generating embedding for: {data['name']}")
        url = os.environ.get("EMBEDDING_URL")
        headers = {
            "X-Embedding-Service-Key": os.environ.get("EMBEDDING_SERVICE_KEY"),
            "Content-Type": "application/json",
        }
        response = requests.post(url, headers=headers, json={"text": sent})
        logger.debug(f"Successfully generated embedding for: {data['name']}")
        return response.json()["embedding"]
    except Exception as e:
        logger.error(
            f"Failed to generate embedding for {data.get('name', 'unknown')}: {e}"
        )
        raise


def safe_api_request(url: str, timeout: int = None) -> Optional[Dict[str, Any]]:
    """Make a safe API request with error handling and logging"""
    timeout = timeout or config.REQUEST_TIMEOUT
    try:
        logger.debug(f"Making API request to: {url}")
        response = session.get(url, timeout=timeout)
        response.raise_for_status()
        logger.debug(f"Successfully received response from: {url}")
        return response.json()
    except requests.exceptions.Timeout:
        logger.error(f"Request timeout for URL: {url}")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed for URL {url}: {e}")
        return None
    except ValueError as e:
        logger.error(f"Failed to parse JSON response from {url}: {e}")
        return None


def update_element(data: Dict[str, Any]) -> bool:
    """Update or insert an element in the Qdrant collection"""
    try:
        # Generate unique ID based on element type
        if data["type"] == "component":
            id_ = f"component_{data['name']}"
        elif data["type"] == "mcp_tool":
            id_ = f"mcp_{data['type_id']}_{data['name']}"
        elif data["type"] == "workflow":
            id_ = f"workflow_{data['type_id']}"
        elif data["type"] == "mcp":
            id_ = f"mcp_{data['type_id']}"
        elif data["type"] == "agent":
            id_ = f"agent_{data['type_id']}"
        else:
            logger.error(f"Unknown element type: {data['type']}")
            return False
        id_ = str(uuid.uuid5(uuid.NAMESPACE_DNS, id_))
        logger.debug(f"Processing element: {id_}")

        # Fetch existing point from Qdrant
        try:
            result = client.retrieve(
                collection_name=collection_name,
                ids=[id_],
                with_payload=True,
                with_vectors=False,
            )
        except Exception:
            result = []

        if len(result) == 0:
            # New element - insert it
            logger.info(f"Inserting new element: {id_}")
            embedding = get_embedding(data)
            metadata = {k: v for k, v in data.items() if v is not None}

            point = PointStruct(id=id_, vector=embedding, payload=metadata)
            client.upsert(collection_name=collection_name, points=[point])
            logger.info(f"Successfully inserted element: {id_}")
            return True
        else:
            try:
                final_ids.remove(id_)
            except ValueError:
                pass
            # Element exists - check if update is needed
            existing_payload = result[0].payload
            if data["type"] == "component":
                embedding = get_embedding(data)
                metadata = {k: v for k, v in data.items() if v is not None}
                point = PointStruct(id=id_, vector=embedding, payload=metadata)
                client.upsert(collection_name=collection_name, points=[point])
                return True

            if "updated_at" not in existing_payload:
                logger.warning(f"No updated_at field found for {id_}, skipping update")
                return True

            # Compare timestamps
            last_updated_str = existing_payload["updated_at"]
            current_updated_str = data["updated_at"]

            try:
                last_updated_date = datetime.fromisoformat(last_updated_str).replace(
                    tzinfo=None
                )
                current_updated_date = datetime.fromisoformat(
                    current_updated_str
                ).replace(tzinfo=None)

                if current_updated_date > last_updated_date and (
                    existing_payload["name"] != data["name"]
                    or existing_payload["description"] != data["description"]
                ):
                    logger.info(
                        f"Updating element: {id_} (last: {last_updated_str}, current: {current_updated_str})"
                    )
                    embedding = get_embedding(data)
                    metadata = {k: v for k, v in data.items() if v is not None}

                    point = PointStruct(id=id_, vector=embedding, payload=metadata)
                    client.upsert(collection_name=collection_name, points=[point])
                    logger.info(f"Successfully updated element: {id_}")
                    return True
                else:
                    logger.debug(f"Element {id_} is up to date")
                    return True

            except ValueError as e:
                logger.error(f"Failed to parse date for {id_}: {e}")
                return False

    except Exception as e:
        logger.error(f"Failed to update element {data.get('name', 'unknown')}: {e}")
        return False


def update_component() -> bool:
    """Update all components from the API"""
    logger.info("Starting component update process...")

    try:
        base_url = config.BASE_URL
        print(base_url)
        if base_url.endswith("/"):
            base_url = base_url[:-1]
        component_url = f"{base_url}/components?refresh=true"
        print(component_url)
        components_data = safe_api_request(component_url)
        if not components_data:
            logger.error("Failed to fetch components data")
            return False

        total_components = sum(
            len(components_data[category]) for category in components_data
        )
        logger.info(
            f"Found {total_components} components across {len(components_data)} categories"
        )

        processed_count = 0
        success_count = 0

        for category in components_data:
            logger.info(f"Processing category: {category}")

            for component_key in components_data[category]:
                component_data = components_data[category][component_key]

                loop_description = {
                    "type": "component",
                    "name": component_data["name"],
                    "description": component_data["description"],
                    "category": component_data["category"],
                }

                if update_element(loop_description):
                    success_count += 1

                processed_count += 1

                if processed_count % 10 == 0:
                    logger.info(
                        f"Processed {processed_count}/{total_components} components"
                    )

        logger.info(
            f"Component update completed: {success_count}/{processed_count} successful"
        )
        return success_count == processed_count

    except Exception as e:
        logger.error(f"Failed to update components: {e}")
        return False


def update_mcp() -> bool:
    """Update all MCPs from the API"""
    logger.info("Starting MCP update process...")

    try:
        # Get first page to determine total pages
        base_url = config.BASE_URL

        if base_url.endswith("/"):
            base_url = base_url[:-1]
        mcp_list_url = f"{base_url}/marketplace/mcps"
        mcp_url = f"{base_url}/marketplace/mcps/"+"{}"
        first_page_url = f"{mcp_list_url}?page=1&page_size={config.PAGE_SIZE}"
        first_page_data = safe_api_request(first_page_url)
        if not first_page_data:
            logger.error("Failed to fetch MCP list")
            return False

        total_pages = first_page_data["metadata"]["total_pages"]
        logger.info(f"Found {total_pages} pages of MCPs to process")

        processed_count = 0
        success_count = 0

        for page in range(1, total_pages + 1):
            logger.info(f"Processing MCP page {page}/{total_pages}")

            page_url = f"{mcp_list_url}?page={page}&page_size={config.PAGE_SIZE}"
            mcps_data = safe_api_request(page_url)
            if not mcps_data:
                logger.error(f"Failed to fetch MCP page {page}")
                continue

            for mcp in mcps_data["data"]:
                mcp_id = mcp["id"]
                logger.debug(f"Processing MCP: {mcp_id}")

                # Get detailed MCP data
                mcp_detail_url = mcp_url.format(mcp_id)
                mcp_data = safe_api_request(mcp_detail_url)
                if not mcp_data:
                    logger.error(f"Failed to fetch MCP details for {mcp_id}")
                    continue

                mcp_info = mcp_data["mcp"]
                tools = mcp_info.get("mcp_tools_config", {}).get("tools", [])
                if (
                    run_data["last_run"]
                    and datetime.fromisoformat(mcp_info["updated_at"]).isoformat()
                    <= run_data["last_run"] and all(str(
                                    uuid.uuid5(
                                        uuid.NAMESPACE_DNS,
                                        f"mcp_{mcp_info['id']}_{tool['name']}",
                                    )
                                ) in final_ids for tool in tools)
                                and str(
                                    uuid.uuid5(
                                        uuid.NAMESPACE_DNS,
                                        f"mcp_{mcp_info['id']}",
                                    )
                                ) in final_ids
                ):
                    try:
                        final_ids.remove(
                            str(
                                uuid.uuid5(
                                    uuid.NAMESPACE_DNS,
                                    f"mcp_{mcp_info['id']}",
                                )
                            )
                        )
                    except ValueError:
                        pass
                    for tool in tools:
                        try:
                            final_ids.remove(
                                str(
                                    uuid.uuid5(
                                        uuid.NAMESPACE_DNS,
                                        f"mcp_{mcp_info['id']}_{tool['name']}",
                                    )
                                )
                            )
                        except ValueError:
                            pass
                        processed_count += 1
                        success_count += 1
                    logger.debug(
                        f"Skipping MCP {mcp_info['name']} as it is not updated since last run"
                    )
                    continue

                logger.debug(f"Found {len(tools)} tools for MCP {mcp_info['name']}")
                loop_description = {
                    "type": "mcp",
                    "type_id": mcp_info["id"],
                    "name": mcp_info["name"],
                    "category": mcp_info["category"],
                    "description": mcp_info["description"],
                    "updated_at": mcp_info["updated_at"],
                    "logo": mcp_info["logo"],
                }
                if update_element(loop_description):
                    success_count += 1

                processed_count += 1
                for tool in tools:
                    loop_description = {
                        "type": "mcp_tool",
                        "type_id": mcp_info["id"],
                        "mcp_name": mcp_info["name"],
                        "updated_at": mcp_info["updated_at"],
                        "category": mcp_info["category"],
                        "name": tool["name"],
                        "description": tool["description"],
                        "logo": mcp_info["logo"],
                    }
                    if update_element(loop_description):
                        success_count += 1

                    processed_count += 1

        logger.info(
            f"MCP update completed: {success_count}/{processed_count} successful"
        )
        return success_count == processed_count

    except Exception as e:
        logger.error(f"Failed to update MCPs: {e}")
        return False


def update_workflow() -> bool:
    """Update all workflows from the API"""
    logger.info("Starting workflow update process...")

    try:
        # Get first page to determine total pages
        base_url = config.BASE_URL

        if base_url.endswith("/"):
            base_url = base_url[:-1]
        workflow_url = f"{base_url}/marketplace/workflows/"+"{}"
        workflow_list_url = f"{base_url}/marketplace/workflows"
        first_page_url = (
            f"{workflow_list_url}?page=1&page_size={config.PAGE_SIZE}"
        )
        first_page_data = safe_api_request(first_page_url)
        if not first_page_data:
            logger.error("Failed to fetch workflow list")
            return False

        total_pages = first_page_data["metadata"]["total_pages"]
        logger.info(f"Found {total_pages} pages of workflows to process")

        processed_count = 0
        success_count = 0

        for page in range(1, total_pages + 1):
            logger.info(f"Processing workflow page {page}/{total_pages}")

            page_url = (
                f"{workflow_list_url}?page={page}&page_size={config.PAGE_SIZE}"
            )
            workflows_data = safe_api_request(page_url)
            if not workflows_data:
                logger.error(f"Failed to fetch workflow page {page}")
                continue

            for workflow in workflows_data["data"]:
                workflow_id = workflow["id"]
                logger.debug(f"Processing workflow: {workflow_id}")

                # Get detailed workflow data
                workflow_detail_url = workflow_url.format(workflow_id)
                workflow_data = safe_api_request(workflow_detail_url)
                if not workflow_data:
                    logger.error(f"Failed to fetch workflow details for {workflow_id}")
                    continue

                workflow_info = workflow_data["workflow"]
                if (
                    run_data["last_run"]
                    and datetime.fromisoformat(workflow_info["updated_at"]).isoformat()
                    <= run_data["last_run"] and str(
                        uuid.uuid5(
                            uuid.NAMESPACE_DNS,
                            f"workflow_{workflow_info['id']}",
                        )
                    ) in final_ids
                ):
                    try:
                        final_ids.remove(
                            str(
                                uuid.uuid5(
                                    uuid.NAMESPACE_DNS,
                                    f"workflow_{workflow_info['id']}",
                                )
                            )
                        )
                    except ValueError:
                        pass
                    logger.debug(
                        f"Skipping workflow {workflow_info['name']} as it is not updated since last run"
                    )
                    continue
                loop_description = {
                    "type": "workflow",
                    "type_id": workflow_info["id"],
                    "name": workflow_info["name"],
                    "category": workflow_info["category"],
                    "description": workflow_info["description"],
                    "updated_at": workflow_info["updated_at"],
                    "logo": workflow_info["image_url"],
                }

                if update_element(loop_description):
                    success_count += 1

                processed_count += 1

        logger.info(
            f"Workflow update completed: {success_count}/{processed_count} successful"
        )
        return success_count == processed_count

    except Exception as e:
        logger.error(f"Failed to update workflows: {e}")
        return False


def update_agent() -> bool:
    """Update all agents from the API"""
    logger.info("Starting agent update process...")
    try:
        base_url = config.BASE_URL
        if base_url.endswith("/"):
            base_url = base_url[:-1]
        agent_url = f"{base_url}/marketplace/agents/"+"{}"
        agent_list_url = f"{base_url}/marketplace/agents"
        first_agent_url = f"{agent_list_url}?page=1&page_size={config.PAGE_SIZE}"
        first_page_data = safe_api_request(first_agent_url)
        if not first_page_data:
            logger.error("Failed to fetch agent list")
            return False
        total_pages = first_page_data["metadata"]["total_pages"]
        success_count = 0
        processed_count = 0
        for page in range(1, total_pages + 1):
            logger.info(f"Processing agent page {page}/{total_pages}")
            page_url = (
                f"{agent_list_url}?page={page}&page_size={config.PAGE_SIZE}"
            )
            agents_data = safe_api_request(page_url)
            agents_data = agents_data["data"]
            for agent in agents_data:
                try:
                    agent_id = agent["id"]
                    agent_detail_url = agent_url.format(agent_id)
                    agent_data = safe_api_request(agent_detail_url)
                    agent_data = agent_data["agent"]
                    if (
                        run_data["last_run"]
                        and datetime.fromisoformat(agent_data["updated_at"]).isoformat()
                        <= run_data["last_run"] and str(
                            uuid.uuid5(
                                uuid.NAMESPACE_DNS,
                                f"agent_{agent_data['id']}",
                            )
                        ) in final_ids
                    ):
                        try:
                            final_ids.remove(
                                str(
                                    uuid.uuid5(
                                        uuid.NAMESPACE_DNS,
                                        f"agent_{agent_data['id']}",
                                    )
                                )
                            )
                        except ValueError:
                            pass
                        logger.debug(
                            f"Skipping agent {agent_data['name']} as it is not updated since last run"
                        )
                        continue
                    loop_description = {
                        "type": "agent",
                        "type_id": agent_data["id"],
                        "name": agent_data["name"],
                        "category": agent_data["category"],
                        "description": agent_data["description"],
                        "updated_at": agent_data["updated_at"],
                        "logo": agent_data["avatar"],
                    }
                    if update_element(loop_description):
                        success_count += 1
                except Exception as e:
                    logger.error(f"Failed to fetch agent details for {agent_id}: {e}")
                processed_count += 1
        logger.info(
            f"Agent update completed: {success_count}/{processed_count} successful"
        )
        return True
    except Exception as e:
        logger.error(f"Failed to fetch agent list: {e}")
        return False


def main() -> None:
    """Main execution function with comprehensive logging and error handling"""
    start_time = time.time()
    logger.info("=" * 60)
    logger.info("Starting RAG deployment update process")
    logger.info("=" * 60)

    results = {"components": False, "mcps": False, "workflows": False, "agents": False}

    try:
        # Update components
        logger.info("Phase 1: Updating components...")
        results["components"] = update_component()

        # Update MCPs
        logger.info("Phase 2: Updating MCPs...")
        results["mcps"] = update_mcp()

        # Update workflows
        logger.info("Phase 3: Updating workflows...")
        results["workflows"] = update_workflow()

        # Update agents
        logger.info("Phase 4: Updating agents...")
        results["agents"] = update_agent()
        # Delete all remaining ids from Qdrant
        # logger.info("Phase 5: Deleting old entries...")
        # logger.info(f"Deleting {len(final_ids)} old entries")
        # logger.info(f"Final ids: {final_ids}")
        # logger.info("Deleting entries...")
        # if len(final_ids) > 0:
        #     client.delete(collection_name=collection_name, points_selector=final_ids)
        run_data["last_run"] = current_run
        with open(metadata_file, "w") as f:
            json.dump(run_data, f)
    except KeyboardInterrupt:
        logger.info("Update process interrupted by user")
    except Exception as e:
        logger.error(f"Unexpected error during update process: {e}")
        raise
    finally:
        logger.info("=" * 60)


if __name__ == "__main__":
    main()
