import json
import logging
from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client
from app.shared.config.base import get_settings
from app.utils.tracing import trace_operation

logger = logging.getLogger(__name__)


class MCPClient:
    """Client for interacting with MCP tools via the gateway."""

    def __init__(self, user_id: str):
        """
        Initialize MCP client with user ID.

        Args:
            user_id: The user ID for authentication
        """
        self.user_id = user_id
        self.settings = get_settings()

    async def execute_tool(
        self,
        mcp_name_slug: str,
        tool_name: str,
        tool_parameters: dict,
    ):
        """
        Execute an MCP tool with the given parameters.

        Args:
            mcp_name_slug: The name slug of the MCP to execute
            tool_name: The name of the tool to execute within the MCP
            tool_parameters: Dictionary of parameters required by the tool

        Returns:
            The result from the MCP tool execution
        """
        with trace_operation(
            "mcp.client.execute_tool",
            attributes={
                "mcp_name_slug": mcp_name_slug,
                "tool_name": tool_name,
                "user_id": self.user_id,
                "parameter_count": len(tool_parameters) if tool_parameters else 0,
            },
        ) as span:
            base_url = self.settings.mcp.gateway_url.rstrip("/")
            mcp_url = f"{base_url}?name_slug={mcp_name_slug}&apiKey={self.settings.mcp.api_key}&userId={self.user_id}"

            # Log MCP execution without sensitive data
            logger.info(
                f"Executing MCP tool - Name: {mcp_name_slug}, Tool: {tool_name}"
            )

            try:
                with trace_operation(
                    "mcp.session.call_tool",
                    attributes={
                        "mcp_name_slug": mcp_name_slug,
                        "tool_name": tool_name,
                    },
                ) as session_span:
                    async with streamablehttp_client(
                        mcp_url,
                        timeout=300,  # 5 minutes timeout
                        sse_read_timeout=300,  # 5 minutes SSE read timeout
                    ) as (
                        read_stream,
                        write_stream,
                        _,
                    ):
                        async with ClientSession(read_stream, write_stream) as session:
                            await session.initialize()
                            result = await session.call_tool(tool_name, tool_parameters)
                            session_span.set_attribute("success", True)
                            span.set_attribute("success", True)
                            return result

            except Exception as e:
                logger.error(f"MCP Execution Error: {str(e)}")
                span.set_attribute("error", True)
                span.record_exception(e)
                raise e
