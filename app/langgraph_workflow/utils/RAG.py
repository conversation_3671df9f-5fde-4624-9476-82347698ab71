import os

import requests
from qdrant_client import QdrantClient
from qdrant_client.models import FieldCond<PERSON>, Filter, MatchAny


class QdrantProvider:
    def __init__(self, host: str, port: int, api_key: str, collection_name: str):
        self.client = QdrantClient(host=host, port=port, api_key=api_key)
        self.collection_name = collection_name

    def search_rag(self, query: str, limit: int = 10):
        url = os.environ.get("EMBEDDING_URL")
        headers = {
            "X-Embedding-Service-Key": os.environ.get("EMBEDDING_SERVICE_KEY"),
            "Content-Type": "application/json",
        }
        response = requests.post(url, headers=headers, json={"text": query})
        query_vector = response.json()["embedding"]
        result = self.client.query_points(
            collection_name=self.collection_name,
            query=query_vector,
            limit=limit,
            query_filter=Filter(
                should=[
                    FieldCondition(
                        key="type",
                        match=MatchAny(any=["mcp_tool", "workflow", "component"]),
                    )
                ]
            ),
        )
        result = [hit.payload for hit in result.points]
        return result

    def search_marketplace(
        self,
        query: str,
        limit: int = 10,
        type_list: list[str] = ["mcp", "workflow", "agent"],
        category: list[str] = [],
    ):
        url = os.environ.get("EMBEDDING_URL")
        headers = {
            "X-Embedding-Service-Key": os.environ.get("EMBEDDING_SERVICE_KEY"),
            "Content-Type": "application/json",
        }
        response = requests.post(url, headers=headers, json={"text": query})
        query_vector = response.json()["embedding"]
        condition = []
        if type_list:
            condition.append(
                FieldCondition(
                    key="type",
                    match=MatchAny(any=type_list),
                )
            )
        if category:
            condition.append(
                FieldCondition(
                    key="category",
                    match=MatchAny(any=category),
                )
            )
        result = self.client.query_points(
            collection_name=self.collection_name,
            query=query_vector,
            limit=limit,
            query_filter=Filter(must=condition) if condition else None,
        )
        result = [hit.payload for hit in result.points]
        return result


QDRANT_HOST = os.environ.get("QDRANT_HOST")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
QDRANT_COLLECTION_NAME = os.environ.get("QDRANT_COLLECTION_NAME")
provider = QdrantProvider(
    host=QDRANT_HOST,
    port=443,
    api_key=QDRANT_API_KEY,
    collection_name=QDRANT_COLLECTION_NAME,
)


def RAG_search(query: str, k: int = 10) -> list:
    return provider.search_rag(query, k)