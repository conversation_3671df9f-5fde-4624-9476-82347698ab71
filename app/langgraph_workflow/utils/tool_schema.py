RAG_TOOL_SCHEMA = {
    "name": "RAG_search",
    "description": "Function take a description query and return the top k nodes which are semantically similar to the description. it return the list of dictionary which contains the type and description of the node. The default value of k is 10.",
    "parameters": {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "The search query to find semantically similar nodes",
            },
            "k": {
                "type": "integer",
                "description": "Number of results to return (default: 10)",
                "default": 10,
            },
        },
        "required": ["query"],
    },
}

GET_CONTEXT_SCHEMA = {
    "name": "get_context",
    "description": "Function take a item return by the RAG search and as a dictionary not the string and return the context, inputs and output of the node.",
    "parameters": {
        "type": "object",
        "properties": {
            "item": {
                "type": "object",
                "description": "The item return by the RAG search",
            },
        },
        "required": ["item"],
    },
}

GET_CURRENT_WORKFLOW_SCHEMA = {
    "name": "get_current_workflow",
    "description": "Returns the current workflow.",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": [],
    },
}

# WorkflowGraph method schemas

ADD_NODE_SCHEMA = {
    "name": "add_node",
    "description": "Add a new node to the workflow graph. Supports component, workflow, and MCP node types.",
    "parameters": {
        "type": "object",
        "properties": {
            "node_id": {
                "type": "string",
                "description": "Unique identifier for the node",
            },
            "label": {
                "type": "string",
                "description": "Display label for the node",
            },
            "OriginalType": {
                "type": "string",
                "description": "The original type/class name of the node",
            },
            "type": {
                "type": "string",
                "enum": ["component", "workflow", "mcp"],
                "description": "The type of node to create",
            },
            "position": {
                "type": "array",
                "items": {"type": "number"},
                "minItems": 2,
                "maxItems": 2,
                "description": "Position coordinates [x, y] for the node",
            },
            "parameters": {
                "type": "object",
                "description": "Configuration parameters for the node",
            },
            "mcp_id": {
                "type": "string",
                "description": "MCP server ID (required for MCP nodes)",
            },
            "workflow_id": {
                "type": "string",
                "description": "Workflow ID (required for workflow nodes)",
            },
            "tool_name": {
                "type": "string",
                "description": "Tool name (required for MCP nodes)",
            },
        },
        "required": [
            "node_id",
            "label",
            "OriginalType",
            "type",
            "position",
            "parameters",
        ],
    },
}

ADD_EDGE_SCHEMA = {
    "name": "add_edge",
    "description": "Add an edge connection between two nodes in the workflow graph.",
    "parameters": {
        "type": "object",
        "properties": {
            "source": {
                "type": "string",
                "description": "ID of the source node",
            },
            "sourceHandle": {
                "type": "string",
                "description": "Name of the output handle on the source node",
            },
            "target": {
                "type": "string",
                "description": "ID of the target node",
            },
            "targetHandle": {
                "type": "string",
                "description": "Name of the input handle on the target node",
            },
        },
        "required": ["source", "sourceHandle", "target", "targetHandle"],
    },
}

DELETE_NODE_SCHEMA = {
    "name": "delete_node",
    "description": "Delete a node from the workflow graph. This will also delete all edges connected to the node.",
    "parameters": {
        "type": "object",
        "properties": {
            "node_id": {
                "type": "string",
                "description": "ID of the node to delete",
            },
        },
        "required": ["node_id"],
    },
}

DELETE_EDGE_SCHEMA = {
    "name": "delete_edge",
    "description": "Delete a specific edge connection between two nodes in the workflow graph.",
    "parameters": {
        "type": "object",
        "properties": {
            "source": {
                "type": "string",
                "description": "ID of the source node",
            },
            "sourceHandle": {
                "type": "string",
                "description": "Name of the output handle on the source node",
            },
            "target": {
                "type": "string",
                "description": "ID of the target node",
            },
            "targetHandle": {
                "type": "string",
                "description": "Name of the input handle on the target node",
            },
        },
        "required": ["source", "sourceHandle", "target", "targetHandle"],
    },
}

UPDATE_NODE_SCHEMA = {
    "name": "update_node",
    "description": "Update the configuration parameters of an existing node in the workflow graph.",
    "parameters": {
        "type": "object",
        "properties": {
            "node_id": {
                "type": "string",
                "description": "ID of the node to update",
            },
            "parameters": {
                "type": "object",
                "description": "New configuration parameters for the node",
            },
        },
        "required": ["node_id", "parameters"],
    },
}

GET_GRAPH_REPR_SCHEMA = {
    "name": "get_graph_repr",
    "description": "Get a simplified representation of the workflow graph containing nodes and edges information.",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": [],
    },
}

COMPILE_SCHEMA = {
    "name": "compile",
    "description": "Compile the workflow graph by validating required parameters, setting defaults, and creating a complete workflow representation with proper layout.",
    "parameters": {
        "type": "object",
        "properties": {},
        "required": [],
    },
}

PLANNER_AGENT_SCHEMA = {
    "name": "planner",
    "description": "Generates the workflow plan and identifies tool availability or alternatives.",
    "parameters": {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "The user's request or main agent's input to determine the workflow's purpose.",
            },
            "previous_plan": {
                "type": "string",
                "description": "The previous plan generated by the planner agent.",
            },
            "feedback": {
                "type": "string",
                "description": "The feedback provided by the user on the previous plan.",
            },
        },
        "required": ["prompt"],
    },
}

WORKFLOW_GENERATION_SCHEMA = {
    "name": "workflow_generation",
    "description": "Generates the finalized, executable workflow.",
    "parameters": {
        "type": "object",
        "properties": {
            "prompt": {
                "type": "string",
                "description": "The user's request or main agent's input to determine the workflow's purpose.",
            },
            "plan": {
                "type": "string",
                "description": "The plan generated by the planner agent.",
            },
        },
        "required": ["prompt", "plan"],
    },
}
