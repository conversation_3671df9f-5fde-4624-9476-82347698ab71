# LangGraph Graph Export & Import Specification

## 1. Purpose

This document defines a **portable, safe, and extensible specification** for exporting and importing LangGraph graphs. The goal is to allow users to:

* Export an entire LangGraph graph (nodes, edges, state schema, and logic)
* Transfer it across systems or environments
* Rehydrate it into a fully executable LangGraph graph

This design explicitly separates **graph structure**, **node logic**, and **execution environment**, aligning with LangGraph’s architecture and avoiding unsafe serialization.

---

## 2. Design Principles

1. **Declarative over imperative** – Graph topology is data, not code
2. **Explicit contracts** – Dependencies and assumptions are declared
3. **No implicit execution** – Importing does not auto-run code
4. **Portable but controlled** – Execution environment decides what is allowed
5. **Composable** – Supports mixing code-based and reference-based nodes

---

## 3. High-Level Export Model

An exported graph consists of five major sections:

```json
{
  "metadata": {},
  "state_schema": {},
  "functions": {},
  "nodes": {},
  "edges": []
}
```

Each section is described in detail below.

---

## 4. Metadata

### Purpose

Provides compatibility and validation information.

### Example

```json
"metadata": {
  "format_version": "1.0",
  "langgraph_version": ">=0.0.30",
  "python": ">=3.10",
  "created_at": "2026-01-01T12:00:00Z"
}
```

### Usage

* Checked at import time
* Import should fail fast if incompatible

---

## 5. State Schema

### Purpose

Defines the **LangGraph state object** used by all nodes.

### Representation

The schema is exported **structurally**, not as executable code.

### Example

```json
"state_schema": {
  "type": "TypedDict",
  "name": "GraphState",
  "fields": {
    "input": "str",
    "output": "str",
    "messages": "list[str]"
  }
}
```

### Import Strategy

* Reconstructed into a `TypedDict`, Pydantic model, or dataclass
* Execution environment chooses implementation

---

## 6. Function Definitions

Functions are exported using **multiple strategies**, depending on their nature.

### 6.1 Function Types

| Type           | Description                         | Storage Strategy   |
| -------------- | ----------------------------------- | ------------------ |
| `source`       | User-defined Python function        | Source code + deps |
| `builtin`      | Python built-in                     | Name-based         |
| `callable_ref` | Lambda, partial, framework callable | Registry key       |

---

### 6.2 Source Functions

#### Example

```json
"normalize": {
  "type": "source",
  "code": "def normalize(x):\n    return (x - np.mean(x)) / np.std(x)",
  "dependencies": {
    "numpy": "np"
  }
}
```

#### Import Behavior

* Dependencies are matched against an allowlist
* Code is validated (AST)
* Function is executed in a sandbox and registered

---

### 6.3 Built-in Functions

#### Example

```json
"length": {
  "type": "builtin",
  "name": "len"
}
```

#### Import Behavior

* Resolved via `builtins.<name>`
* Fails if not present

---

### 6.4 Callable References (Registry-based)

Used for:

* Lambdas
* Partials
* Decorated functions
* Framework utilities

#### Example

```json
"double": {
  "type": "callable_ref",
  "registry_key": "double"
}
```

#### Import Behavior

* Looked up in a pre-defined callable registry
* Import fails if missing

---

## 7. Node Definitions

### Purpose

Maps **graph node IDs** to **functions**.

### Example

```json
"nodes": {
  "start": {
    "function": "load_input"
  },
  "process": {
    "function": "normalize"
  },
  "end": {
    "function": "save_output"
  }
}
```

### Notes

* Nodes reference functions by name
* One function can be reused by multiple nodes

---

## 8. Edges

### Purpose

Defines graph topology and flow.

### Example

```json
"edges": [
  ["start", "process"],
  ["process", "end"]
]
```

### Conditional Edges (Optional)

```json
["process", "end", {"condition": "is_complete"}]
```

Conditions are resolved via the same function mechanism.

---

## 9. Export Process (Producer Side)

1. Traverse LangGraph structure
2. Extract state schema
3. Classify each node callable
4. Export functions using correct strategy
5. Export nodes and edges
6. Validate export completeness

---

## 10. Import Process (Consumer Side)

1. Validate metadata compatibility
2. Reconstruct state schema
3. Resolve and register all functions
4. Rebuild LangGraph nodes
5. Attach edges
6. Compile graph

Import **must fail** if any dependency or function cannot be resolved.

---

## 11. Security Considerations

* No auto-installation of packages
* No implicit imports
* Source functions must be sandboxed
* AST validation required
* Registry-based callables must be explicitly approved

---

## 12. Non-Goals

This specification explicitly does **not** support:

* Serializing closures
* Serializing runtime state
* Serializing threads or async tasks
* Cross-language execution

---

## 13. Conceptual Summary

> A LangGraph export is a **declarative graph definition plus a set of executable contracts**.

LangGraph remains the execution engine; this format defines **what to execute and how to reconstruct it safely**.

---

## 14. Nested Functions and Closures

This section defines how **nested functions and closures** are handled during graph export and import.

### 14.1 Background

A nested function is a function defined inside another function:

```python
def node_fn(state):
    def helper(x):
        return x * 2
    return helper(state["value"])
```

In Python, nested functions:

* Do not have a stable, top-level identity
* May capture variables from the enclosing scope (closures)
* Cannot be safely or portably serialized as independent artifacts

---

### 14.2 Supported Patterns

#### Inline Nested Helpers (No Closure)

```python
def node_fn(state):
    def helper(x):
        return x * 2
    return helper(state["value"])
```

**Status:** Conditionally supported

**Rules:**

* Nested helper must not capture outer variables
* Helper is treated as an implementation detail
* Only the parent function is exported
* Helper cannot be reused by other nodes

---

#### Lifted Helpers (Recommended)

```python
def helper(x):
    return x * 2


def node_fn(state):
    return helper(state["value"])
```

**Status:** Fully supported

**Rules:**

* Helper is exported as a normal `source` function
* Dependencies are resolved explicitly
* Preferred pattern for reusable logic

---

### 14.3 Unsupported Patterns

#### Nested Functions with Closures

```python
def node_fn(state):
    factor = state["factor"]
    def helper(x):
        return x * factor
```

**Status:** Not supported

**Reason:**

* Closure variables cannot be serialized or reconstructed
* Behavior depends on runtime state

**Exporter Behavior:**

* Export must fail with a clear diagnostic message

---

#### Nested Lambdas

```python
def node_fn(state):
    f = lambda x: x * 2
```

**Status:** Not supported for export

**Resolution:**

* Lift lambda to a top-level named function
* Or register it explicitly as a `callable_ref`

---

### 14.4 Detection and Validation

Exporters **must** perform the following checks:

* AST scan for nested `def` statements
* Detection of closures via `__closure__`
* Rejection of nested functions that capture outer variables

---

### 14.5 Rationale

These rules enforce that exported node functions are:

* Top-level
* Deterministic
* Portable across environments

They align with LangGraph’s design philosophy and prevent subtle, hard-to-debug failures.

---

## 15. Known Problems, Risks, and Limitations

This section documents **inherent problems and trade-offs** in exporting and importing executable LangGraph graphs. These are not implementation bugs, but **fundamental risks** that must be understood by users and maintainers.

---

### 15.1 Semantic Drift

**Problem:**
An imported graph may execute successfully but produce different results than when it was exported.

**Causes:**

* Dependency version changes (e.g., NumPy behavior changes)
* Updated callable registries
* Modified source functions with the same name

**Risk Level:** High (silent correctness failures)

**Mitigations:**

* Dependency version constraints
* Hashing of function source code
* Versioned registry keys (e.g., `normalize@v1`)
* Optional strict import mode

---

### 15.2 False Portability

**Problem:**
Graphs appear portable but depend on undeclared external context.

**Examples:**

* Environment variables
* File system access
* Implicit globals

**Mitigations:**

* AST validation for undeclared globals
* Explicit environment contract documentation
* Import-time validation and failure

---

### 15.3 Sandbox Illusion

**Problem:**
Python-level sandboxing is not secure, especially when native extensions are allowed.

**Examples:**

* NumPy executing native code
* Attribute access escape techniques

**Mitigations:**

* OS-level isolation (Docker, gVisor)
* CPU and memory limits
* No network access

---

### 15.4 Graph Integrity Errors

**Problem:**
Graphs may be structurally invalid after import.

**Examples:**

* Missing nodes
* Missing functions
* Invalid entry points

**Mitigations:**

* Graph validation phase
* Fail-fast import behavior

---

### 15.5 State Schema Mismatch

**Problem:**
Nodes may assume incompatible state shapes.

**Mitigations:**

* Single canonical state schema
* Optional runtime schema validation
* Explicit node read/write contracts

---

### 15.6 Callable Registry Fragility

**Problem:**
Registry-based callables (e.g., lambdas) may be missing or altered.

**Mitigations:**

* Registry manifests
* Callable hashing or signatures
* Import-time completeness checks

---

### 15.7 Debuggability Degradation

**Problem:**
Errors are harder to trace after rehydration.

**Mitigations:**

* Preserve original filenames in compilation
* Attach node and function identifiers to errors
* Provide export preview and diagnostics tools

---

### 15.8 Testing Blind Spots

**Problem:**
Graphs are not tested in their serialized form.

**Mitigations:**

* Mandatory round-trip tests
* Export/import CI validation

---

### 15.9 Partial Support Confusion

**Problem:**
Users may not know which node or function types are supported.

**Mitigations:**

* Explicit support matrix
* Clear import-time error messages
* Documentation of unsupported patterns

---

### 15.10 Performance and Scaling Risks

**Problem:**
Large graphs or repeated imports can degrade performance.

**Mitigations:**

* Cache compiled functions
* Hash-based memoization
* Lazy loading strategies

---

### 15.11 Versioning Explosion

**Problem:**
Multiple independent version axes increase maintenance complexity.

**Mitigations:**

* Explicit version metadata
* Compatibility matrices
* Clear deprecation policies

---

### 15.12 Scope and Guarantee Limitations

This export/import mechanism does **not guarantee**:

* Bit-for-bit reproducibility
* Security against malicious code
* Cross-language portability

It provides **controlled portability**, not full determinism.

---

## 16. Future Extensions (Optional)

* Function versioning and hashing
* Graph signatures
* Encrypted exports
* Remote callable registries
* CLI tooling for export/import
---

## 17. Reference code
```python
import inspect
import textwrap
import ast
import json
from types import FunctionType
import sys

# -------------------------------
# Helper: get called function names
# -------------------------------
def get_called_function_names(fn):
    """Return a set of function names called inside fn (ignoring built-ins)."""
    src = textwrap.dedent(inspect.getsource(fn))
    tree = ast.parse(src)
    calls = set()
    for node in ast.walk(tree):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Name):
                calls.add(node.func.id)
    return calls

# -------------------------------
# Export Function (auto-detect helpers)
# -------------------------------
def export_function_auto(fn, module_globals=None):
    """
    Export a function and all its dependencies automatically.
    module_globals: dict of global symbols to search for helpers, default to caller globals.
    """
    if module_globals is None:
        module_globals = sys._getframe(1).f_globals  # caller globals

    exported = {}
    processed = set()

    def _process(f):
        if f.__name__ in processed:
            return
        processed.add(f.__name__)

        # Extract source
        src = textwrap.dedent(inspect.getsource(f))
        tree = ast.parse(src)

        # Detect nested functions
        nested_names = []
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                for child in node.body:
                    if isinstance(child, ast.FunctionDef):
                        nested_names.append(child.name)

        exported[f.__name__] = {
            "type": "source",
            "code": src,
            "nested": nested_names
        }

        # Detect called functions in the body
        called = get_called_function_names(f)
        for name in called:
            # Include only top-level functions defined in module_globals
            if name in module_globals and isinstance(module_globals[name], FunctionType):
                _process(module_globals[name])

    _process(fn)
    return exported

def export_functions_to_json_auto(fn_list, file_path, module_globals=None):
    result = {}
    for fn in fn_list:
        result.update(export_function_auto(fn, module_globals=module_globals))

    with open(file_path, "w") as f:
        json.dump(result, f, indent=4)
    print(f"Exported {len(fn_list)} functions (with dependencies) to {file_path}")


# -------------------------------
# Import Functions
# -------------------------------
def import_functions_from_json(file_path, global_ns=None):
    with open(file_path, "r") as f:
        data = json.load(f)

    if global_ns is None:
        global_ns = {}

    for name, entry in data.items():
        if entry["type"] == "source":
            exec(entry["code"], global_ns)

    return {name: global_ns[name] for name in data.keys()}
```

---

End of document.
