import json
import os

from langchain_core.messages import ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

from app.langgraph_workflow.models.state import State
from app.langgraph_workflow.utils.RAG import RAG_search
from app.langgraph_workflow.utils.tool_schema import (
    GET_CURRENT_WORKFLOW_SCHEMA,
    RAG_TOOL_SCHEMA,
)


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        client_args={
            "api_key": REQUESTY_API_KEY,
            "base_url": "https://router.requesty.ai/v1",
        },
        model_id="anthropic/claude-sonnet-4",
    )
    llm.bind_tools([RAG_TOOL_SCHEMA, GET_CURRENT_WORKFLOW_SCHEMA])
    return llm


def agent_node(state: State) -> State:
    update = []
    llm = get_llm()
    response = llm.invoke(state.planner_messages)
    update.append({"planner_messages": [response]})
    if not response.tool_calls:
        update.append({"plan": response.content})
        update.append({"stage": "main"})
    return update


def tools_node(state: State) -> State:
    update = []
    tool_calls = state.planner_messages[-1].tool_calls
    for tool_call in tool_calls:
        if tool_call.name == "RAG_search":
            result = RAG_search(tool_call.args["query"], tool_call.args.get("k", 10))
            for i in range(len(result)):
                result[i].pop("updated_at")
                result[i].pop("logo")
            update.append(
                {
                    "planner_messages": [
                        ToolMessage(
                            content=json.dumps(result), tool_call_id=tool_call.id
                        )
                    ]
                }
            )
        if tool_call.name == "get_current_workflow":
            update.append(
                {
                    "planner_messages": [
                        ToolMessage(
                            content=json.dumps(state.workflow_graph.get_graph_repr()),
                            tool_call_id=tool_call.id,
                        )
                    ]
                }
            )
    return update


def router(state: State):
    tool_calls = state.planner_messages[-1].tool_calls
    if tool_calls:
        return "tools"
    else:
        return "end"


def create_planner_graph():
    planner_graph = StateGraph(State)
    planner_graph.add_node(agent_node, "agent")
    planner_graph.add_node(tools_node, "tools")
    planner_graph.add_conditional_edges("agent", router, {"tools": "tools", "end": END})
    planner_graph.set_entry_point("agent")
    return planner_graph.compile()
