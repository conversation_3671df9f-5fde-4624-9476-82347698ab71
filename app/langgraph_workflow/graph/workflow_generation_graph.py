import json
import os

from langchain_core.messages import ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph
from langgraph.types import Overwrite

from app.langgraph_workflow.models.state import State
from app.langgraph_workflow.utils.context import get_context
from app.langgraph_workflow.utils.RAG import RAG_search
from app.langgraph_workflow.utils.tool_schema import (
    ADD_EDGE_SCHEMA,
    ADD_NODE_SCHEMA,
    COMPILE_SCHEMA,
    DELETE_EDGE_SCHEMA,
    DELETE_NODE_SCHEMA,
    GET_CONTEXT_SCHEMA,
    GET_GRAPH_REPR_SCHEMA,
    RAG_TOOL_SCHEMA,
    UPDATE_NODE_SCHEMA,
)


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        client_args={
            "api_key": REQUESTY_API_KEY,
            "base_url": "https://router.requesty.ai/v1",
        },
        model_id="anthropic/claude-sonnet-4",
    )
    llm.bind_tools(
        [
            RAG_TOOL_SCHEMA,
            GET_CONTEXT_SCHEMA,
            ADD_NODE_SCHEMA,
            ADD_EDGE_SCHEMA,
            DELETE_NODE_SCHEMA,
            DELETE_EDGE_SCHEMA,
            UPDATE_NODE_SCHEMA,
            GET_GRAPH_REPR_SCHEMA,
            COMPILE_SCHEMA,
        ]
    )
    return llm


def agent_node(state: State):
    update = []
    llm = get_llm()
    response = llm.invoke(state.workflow_generation_messages)
    update.append({"workflow_generation_messages": [response]})
    if not response.tool_calls:
        update.append({"stage": "main"})
    return update


def tools_node(state: State):
    update = []
    tool_calls = state.workflow_generation_messages[-1].tool_calls
    for tool_call in tool_calls:
        if tool_call.name == "RAG_search":
            result = RAG_search(tool_call.args["query"], tool_call.args.get("k", 10))
            result = ToolMessage(content=json.dumps(result), tool_call_id=tool_call.id)
            update.append({"rag_message_ids": [result.id]})
            update.append({"workflow_generation_messages": [result]})
        if tool_call.name == "get_context":
            result = get_context(tool_call.args["item"])
            result = ToolMessage(content=result, tool_call_id=tool_call.id)
            update.append({"get_context_message_ids": [result.id]})
            update.append({"workflow_generation_messages": [result]})
        if tool_call.name == "add_node":
            try:
                state.workflow_graph.add_node(
                    tool_call.args["node_id"],
                    tool_call.args["label"],
                    tool_call.args["OriginalType"],
                    tool_call.args["type"],
                    tool_call.args["position"],
                    tool_call.args["parameters"],
                    tool_call.args.get("mcp_id"),
                    tool_call.args.get("workflow_id"),
                    tool_call.args.get("tool_name"),
                )
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content="Node added successfully.",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
                rag_message_ids = state.rag_message_ids
                get_context_message_ids = state.get_context_message_ids
                update.append({"rag_message_ids": Overwrite([])})
                update.append({"get_context_message_ids": Overwrite([])})
                for message_id in rag_message_ids:
                    update.append({"workflow_generation_messages": [ToolMessage(content="Remove", tool_call_id=message_id)]})
                for message_id in get_context_message_ids:
                    update.append({"workflow_generation_messages": [ToolMessage(content="Remove", tool_call_id=message_id)]})
            except Exception as e:
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=f"Error adding node: {str(e)}",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
        if tool_call.name == "add_edge":
            try:
                state.workflow_graph.add_edge(
                    tool_call.args["source"],
                    tool_call.args["sourceHandle"],
                    tool_call.args["target"],
                    tool_call.args["targetHandle"],
                )
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content="Edge added successfully.",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
            except Exception as e:
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=f"Error adding edge: {str(e)}",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
        if tool_call.name == "delete_node":
            try:
                state.workflow_graph.delete_node(tool_call.args["node_id"])
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content="Node deleted successfully.",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
            except Exception as e:
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=f"Error deleting node: {str(e)}",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
        if tool_call.name == "delete_edge":
            try:
                state.workflow_graph.delete_edge(
                    tool_call.args["source"],
                    tool_call.args["sourceHandle"],
                    tool_call.args["target"],
                    tool_call.args["targetHandle"],
                )
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content="Edge deleted successfully.",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
            except Exception as e:
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=f"Error deleting edge: {str(e)}",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
        if tool_call.name == "update_node":
            try:
                state.workflow_graph.update_node(
                    tool_call.args["node_id"], tool_call.args["parameters"]
                )
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content="Node updated successfully.",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
            except Exception as e:
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=f"Error updating node: {str(e)}",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
        if tool_call.name == "get_graph_repr":
            try:
                result = state.workflow_graph.get_graph_repr()
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=json.dumps(result), tool_call_id=tool_call.id
                            )
                        ]
                    }
                )
            except Exception as e:
                update.append(
                    {
                        "workflow_generation_messages": [
                            ToolMessage(
                                content=f"Error getting graph representation: {str(e)}",
                                tool_call_id=tool_call.id,
                            )
                        ]
                    }
                )
    update.append({"workflow_graph": state.workflow_graph})
    return update


def router(state: State):
    tool_calls = state.workflow_generation_messages[-1].tool_calls
    if tool_calls:
        return "tools"
    else:
        return "end"


def create_workflow_generation_graph():
    workflow_generation_graph = StateGraph(State)
    workflow_generation_graph.add_node(agent_node, "agent")
    workflow_generation_graph.add_node(tools_node, "tools")
    workflow_generation_graph.add_conditional_edges(
        "agent", router, {"tools": "tools", "end": END}
    )
    workflow_generation_graph.set_entry_point("agent")
    return workflow_generation_graph.compile()
