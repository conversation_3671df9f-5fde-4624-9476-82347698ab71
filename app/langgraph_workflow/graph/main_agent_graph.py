import json
import os

from langchain_core.messages import ToolMessage
from langchain_openai import ChatOpenAI
from langgraph.graph import END, StateGraph

from app.langgraph_workflow.models.state import State
from app.langgraph_workflow.utils.context import get_context
from app.langgraph_workflow.utils.RAG import RAG_search
from app.langgraph_workflow.utils.tool_schema import (
    GET_CURRENT_WORKFLOW_SCHEMA,
    PLANNER_AGENT_SCHEMA,
    WORKFLOW_GENERATION_SCHEMA,
)
from app.langgraph_workflow.graph.planner_agent_graph import create_planner_graph
from app.langgraph_workflow.graph.workflow_generation_graph import create_workflow_generation_graph


def get_llm():
    REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
    llm = ChatOpenAI(
        client_args={
            "api_key": REQUESTY_API_KEY,
            "base_url": "https://router.requesty.ai/v1",
        },
        model_id="anthropic/claude-sonnet-4",
    )
    llm.bind_tools([PLANNER_AGENT_SCHEMA, WORKFLOW_GENERATION_SCHEMA, GET_CURRENT_WORKFLOW_SCHEMA])
    return llm


def agent_node(state: State):
    update = []
    llm = get_llm()
    response = llm.invoke(state.main_agent_messages)
    update.append({"main_agent_messages": [response]})
    if not response.tool_calls:
        update.append({"stage": "end"})
    else:
        tool_call = response.tool_calls[0]
        if tool_call.name == "plan":
            update.append({"stage": "planning"})
        elif tool_call.name == "generate_workflow":
            update.append({"stage": "workflow_generation"})
    return update

def get_current_workflow(state: State):
    update = []
    tool_call = state.main_agent_messages[-1].tool_calls[0]
    update.append(
        {
            "main_agent_messages": [
                ToolMessage(
                    content=json.dumps(state.workflow_graph.get_graph_repr()),
                    tool_call_id=tool_call.id,
                )
            ]
        }
    )
    return update

def router(state: State):
    if state.stage == "planning":
        return "planner_agent"
    elif state.stage == "workflow_generation":
        return "workflow_generation_agent"
    elif state.stage == "end":
        return "end"
    return "get_current_workflow"

def create_main_agent_graph():
    planner_agent = create_planner_graph()
    workflow_generation_agent = create_workflow_generation_graph()
    
    main_agent_graph = StateGraph(State)
    main_agent_graph.add_node(agent_node, "agent")
    main_agent_graph.add_node(planner_agent, "planner_agent")
    main_agent_graph.add_node(workflow_generation_agent, "workflow_generation_agent")
    main_agent_graph.add_node(get_current_workflow, "get_current_workflow")
    main_agent_graph.add_conditional_edges("agent", router, {"planner_agent": "planner_agent", "workflow_generation_agent": "workflow_generation_agent", "get_current_workflow": "get_current_workflow", "end": END})
    main_agent_graph.add_edge("planner_agent", "agent")
    main_agent_graph.add_edge("workflow_generation_agent", "agent")
    main_agent_graph.add_edge("get_current_workflow", "agent")
    main_agent_graph.set_entry_point("agent")
    return main_agent_graph.compile()
