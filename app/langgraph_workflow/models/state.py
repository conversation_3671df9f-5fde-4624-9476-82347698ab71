import operator
from typing import Annotated, List, Literal, Optional

from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from operator import add
from pydantic import BaseModel, Field

from app.langgraph_workflow.models.workflowGraph import WorkflowGraph

class State(BaseModel):
    main_agent_messages: Annotated[List[AnyMessage], add_messages]
    planner_messages: Annotated[List[AnyMessage], add_messages]
    workflow_generation_messages: Annotated[List[AnyMessage], add_messages]

    plan: Optional[str] = None
    feedback: Optional[str] = None

    workflow_graph: WorkflowGraph
    rag_message_ids: Annotated[List[str], add]
    get_context_message_ids: Annotated[List[str], add]

    stage: Literal["main", "planning", "workflow_generation", "end"] = "planning"
