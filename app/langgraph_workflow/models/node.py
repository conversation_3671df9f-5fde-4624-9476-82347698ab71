import copy

import requests
from pydantic import BaseModel, Field


class NodeData(BaseModel):
    label: str
    type: str
    originalType: str
    definition: dict
    config: dict


class Node(BaseModel):
    id: str
    type: str
    position: dict[str, float]
    data: NodeData
    width: int = 388
    height: int
    selected: bool = False
    dragging: bool = False
    style: dict = {"opacity": 1}


COMPONENT_TYPE_EXCEPTIONS = {
    "AgenticAI": "agent",
    "LoopNode": "loop",
}

START_NODE = {
    "id": "start-node",
    "type": "WorkflowNode",
    "data": {
        "label": "Start",
        "type": "component",
        "originalType": "StartNode",
        "definition": {
            "name": "StartNode",
            "display_name": "Start",
            "description": "The starting point for all workflows. Only nodes connected to this node will be executed.",
            "category": "IO",
            "icon": "Play",
            "beta": False,
            "requires_approval": False,
            "visible_in_logs_ui": False,
            "inputs": [],
            "outputs": [
                {
                    "name": "flow",
                    "display_name": "Flow",
                    "output_type": "Any",
                    "semantic_type": None,
                    "method": None,
                }
            ],
            "is_valid": True,
            "path": "components.io.startnode",
            "interface_issues": [],
        },
        "config": {"collected_parameters": {}},
    },
    "height": 194,
    "selected": False,
    "dragging": False,
    "style": {"opacity": 1},
}


def request_info(url):
    response = requests.get(url)
    response.raise_for_status()
    return response.json()


def calculate_node_height(total_handles: int) -> int:
    base_height = 90

    if total_handles <= 2:
        spacing_per_handle = 35
    elif total_handles == 3:
        spacing_per_handle = 25
    else:
        spacing_per_handle = 20

    height = base_height + total_handles * spacing_per_handle
    height = int(height * 388 / 208)
    return height


def get_component_node(
    node_id: str,
    label: str,
    OriginalType: str,
    position: tuple[float, float],
    parameters: dict,
):
    if OriginalType == "StartNode":
        node = Node(position={"x": position[0], "y": position[1]}, **START_NODE)
        return node
    component_url = "https://app-dev.rapidinnovation.dev/api/v1/components"
    components_data = request_info(component_url)
    for _, components in components_data.items():
        if OriginalType in components:
            definition = components[OriginalType]
    height = calculate_node_height(
        len(definition["inputs"]) + len(definition["outputs"])
    )
    node = Node(
        id=node_id,
        type="WorkflowNode",
        position={"x": position[0], "y": position[1]},
        data=NodeData(
            label=label,
            type=COMPONENT_TYPE_EXCEPTIONS.get(OriginalType, "component"),
            originalType=OriginalType,
            definition=definition,
            config=parameters,
        ),
        height=height,
    )
    return node


def get_workflow_node(
    node_id: str,
    label: str,
    OriginalType: str,
    position: tuple[float, float],
    parameters: dict,
    workflow_id: str,
):
    workflow_url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/workflows/{workflow_id}"
    workflow_data = request_info(workflow_url)
    start_nodes = workflow_data.get("start_nodes", [])
    inputs = []
    for start_node in start_nodes:
        is_handle_value = start_node.get("type") == "handle"
        field_name = start_node.get("field", "input_data")

        input_item = {
            "name": field_name,
            "display_name": field_name.replace("_", " ").title(),
            "info": f"Input field: {field_name}",
            "input_type": "string",
            "required": True,
            "is_handle": is_handle_value,
            "is_list": False,
            "real_time_refresh": False,
            "advanced": False,
            "value": None,
            "options": None,
            "visibility_rules": None,
            "visibility_logic": "OR",
            "requirement_rules": None,
            "requirement_logic": "OR",
            "transition_id": start_node.get("transition_id"),
        }
        inputs.append(input_item)
    outputs = [
        {
            "name": "execution_status",
            "display_name": "Execution Status",
            "output_type": "string",
        },
        {
            "name": "workflow_execution_id",
            "display_name": "Execution ID",
            "output_type": "string",
        },
        {"name": "message", "display_name": "Message", "output_type": "string"},
    ]
    workflow_info = {
        "id": workflow_id,
        "name": workflow_data.get("name", ""),
        "description": workflow_data.get("description", ""),
        "workflow_url": workflow_data.get("workflow_url"),
        "builder_url": workflow_data.get("builder_url"),
        "start_nodes": workflow_data.get("start_nodes", []),
        "owner_id": workflow_data.get("owner_id"),
        "user_ids": (
            [workflow_data.get("owner_id")] if workflow_data.get("owner_id") else []
        ),
        "owner_type": "user",
        "workflow_template_id": None,
        "template_owner_id": None,
        "template_owner_type": None,
        "is_imported": False,
        "version": workflow_data.get("version", "1.0.0"),
        "visibility": workflow_data.get("visibility", "private").lower(),
        "category": workflow_data.get("category"),
        "tags": workflow_data.get("tags"),
        "status": workflow_data.get("status", "active"),
        "is_changes_marketplace": False,
        "is_customizable": True,
        "auto_version_on_update": False,
        "created_at": workflow_data.get("created_at"),
        "updated_at": workflow_data.get("updated_at"),
        "available_nodes": workflow_data.get("available_nodes") or [],
        "is_updated": True,
        "source_version_id": workflow_data.get("source_version_id"),
    }
    definition = {
        "name": f"workflow-{workflow_id}",
        "display_name": workflow_data.get("name", ""),
        "description": workflow_data.get("description", ""),
        "category": "Workflows",
        "icon": "Workflow",
        "beta": False,
        "path": f"workflow.{workflow_id}",
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        "type": "Workflow",
        "workflow_info": workflow_info,
    }
    height = calculate_node_height(len(inputs) + len(outputs))
    node = Node(
        id=node_id,
        type="WorkflowNode",
        position={"x": position[0], "y": position[1]},
        data=NodeData(
            label=label,
            type="component",
            originalType=f"workflow-{workflow_id}",
            definition=definition,
            config=parameters,
        ),
        height=height,
    )
    return node


def _build_mcp_inputs(props: dict, required: set) -> list:
    """Build input configuration for MCP node."""
    inputs = []

    for name, schema in props.items():
        input_type = schema.get("type")
        itype = (
            "array"
            if input_type == "array" or "items" in schema
            else input_type or "string"
        )

        inputs.append(
            {
                "name": name,
                "display_name": name[0].upper() + name[1:].replace("_", " "),
                "info": schema.get("description", ""),
                "input_type": itype,
                "input_types": [itype, "Any"],
                "required": name in required,
                "is_handle": True,
                "is_list": itype == "array",
                "real_time_refresh": False,
                "advanced": False,
                "value": None,
                "options": None,
                "visibility_rules": None,
                "visibility_logic": "OR",
                "validation": {},
            }
        )

    return inputs


def _build_mcp_outputs(outputs_schema: dict, has_output: bool) -> list:
    """Build output configuration for MCP node."""
    outputs = []

    if has_output:
        for name, schema in outputs_schema.items():
            outputs.append(
                {
                    "name": name,
                    "display_name": schema.get("title", name),
                    "output_type": schema.get("type", "Any"),
                }
            )
    else:
        outputs.append(
            {"name": "result", "display_name": "Result", "output_type": "Any"}
        )

    return outputs


def get_mcp_node(
    node_id: str,
    label: str,
    OriginalType: str,
    position: tuple[float, float],
    parameters: dict,
    mcp_id: str,
    tool_name: str,
):
    mcp_url = f"https://app-dev.rapidinnovation.dev/api/v1/marketplace/mcps/{mcp_id}"
    mcp_data = request_info(mcp_url)
    tools = mcp_data.get("mcp_tools_config", {}).get("tools", [])
    tool = next((tool for tool in tools if tool["name"] == tool_name), None)
    input_schema = tool.get("input_schema", {}) or {}
    output_schema = tool.get("output_schema", {}) or {}
    inputs = _build_mcp_inputs(
        input_schema.get("properties", {}), set(input_schema.get("required", []))
    )
    outputs = _build_mcp_outputs(
        output_schema.get("properties", {}), bool(output_schema)
    )
    logo = mcp_data.get("logo", "")
    icon = logo.split("/")[-1].split(".")[0].capitalize() if logo else ""
    definition = {
        "name": OriginalType,
        "display_name": mcp_data.get("name", ""),
        "description": mcp_data.get("description", ""),
        "category": mcp_data.get("category", ""),
        "icon": icon,
        "beta": False,
        "inputs": inputs,
        "outputs": outputs,
        "is_valid": True,
        "type": "MCP",
        "logo": logo,
        "mcp_info": {
            "server_id": mcp_id,
            "server_path": mcp_data.get("hosted_url", ""),
            "tool_name": tool_name,
            "input_schema": input_schema,
            "output_schema": output_schema,
        },
        "integrations": mcp_data.get("integrations", []),
        "name_slug": mcp_data.get("name_slug", ""),
        "path": "mcp." + OriginalType.lower(),
        "platform": mcp_data.get("platform", ""),
        "visible_in_logs_ui": True,
    }
    height = calculate_node_height(len(inputs) + len(outputs))
    node = Node(
        id=node_id,
        type="WorkflowNode",
        position={"x": position[0], "y": position[1]},
        data=NodeData(
            label=label,
            type="mcp",
            originalType=OriginalType,
            definition=definition,
            config=parameters,
        ),
        height=height,
    )
    return node
