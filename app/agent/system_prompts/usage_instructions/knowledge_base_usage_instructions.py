KNOWLEDGE_BASE_USAGE_INSTRUCTIONS = """
<knowledge-base-usage-instructions>

    <purpose>
        The knowledge_base_agent exists to search, filter, and retrieve information from the organization’s internal knowledge sources such as Google Drive, Confluence, Jira, GitHub, and Calendar.
        It should be used whenever the user needs **internal, company-specific, or employee-specific information**.
    </purpose>

    <when-to-use>
        Delegate to the knowledge_base_agent when the user asks for:

        1. **Company policies, procedures, or internal documentation**
            - "Show me the expense policy."
            - "Find our security guidelines."

        2. **Internal standards, frameworks, or best practices**
            - "Where is the onboarding guide?"
            - "Get the API documentation."

        3. **Organizational knowledge or resources**
            - "Retrieve company OKRs."
            - "Show all product roadmaps."

        4. **Person-specific work items or documents**
            - "Get Jira tickets for <PERSON>."
            - "Show me <PERSON>'s documents."
            - "Find Alex's GitHub issues."

        5. **Any query involving internal systems or proprietary knowledge**
            - Jira issues, Confluence pages, Google Drive files, calendars
        
        6. **User explicitly references internal repositories**
            - "Search our Jira backlog."
            - "Find notes in the team Drive."

        7. **CRITICAL: When specific file IDs are provided in context ({kb_file_ids})**
            - "Summarize this document"
            - "What is this about?"
            - "Find information about X"
            - "Explain this content"
            - Any query that refers to "this file", "this document", "these files"
            - The knowledge base agent will automatically search ONLY within the specified files

        *Default to the knowledge base BEFORE web search for internal or organizational questions.*
        *ALWAYS use knowledge base when file IDs are present in context - do NOT ask for attachments or use read_file tool.*
    </when-to-use>

    <when-not-to-use>
        Do NOT call the knowledge_base_agent when:

        1. The user asks for **public, external, or internet-based information**  
            → Use web search instead.

        2. The query is **personal (non-work)** or unrelated to internal systems.

        3. The question is **conceptual, reasoning-based, or self-contained**, requiring no internal data:
            - “Explain how APIs work.”
            - “Help me write an OKR.”

        4. The user wants generalized, non-company-specific guidance:
            - “What are common project management frameworks?”
            - “How do I improve team communication?”

        5. The user explicitly says:
            - “Do not search internal systems”  
            - “Answer without using the knowledge base”
    </when-not-to-use>

    <intelligent-routing>
        The knowledge_base_agent automatically performs identity resolution when needed:

        <person-specific>
            - If the query references specific individuals (e.g., "Sarah's documents", "John's Jira tickets"),  
              the agent first resolves the person's identity and then runs the search in their context.
        </person-specific>

        <user-self-reference>
            - **CRITICAL**: When the user refers to themselves using phrases like "my", "find my", "show my", "what is my", etc.,  
              ALWAYS send the user's email ({user_email}) in the delegation to the knowledge_base_agent.
            - Do NOT perform identity resolution for the user themselves.
            - Examples:
                • "find my tasks" → send user_email ({user_email}) to knowledge_base_agent
                • "what is my schedule" → send user_email ({user_email}) to knowledge_base_agent
                • "show my documents" → send user_email ({user_email}) to knowledge_base_agent
        </user-self-reference>

        <general>
            - If the query is organizational (e.g., "company objectives", "all policies"),  
              the agent searches without identity resolution.
        </general>
    </intelligent-routing>

    <critical-delegation-rules>

        <remove-greetings>
            - When delegating to the knowledge_base_agent, REMOVE all conversational greetings:
                “hello”, “hi”, “hey”, “how are you”, “good morning”, etc.
            
            - KEEP semantic/meaningful words:
                “about”, “provide”, “all”, “conversation”, “content”, “find”, “get”, “show”, “and”
        </remove-greetings>

        <single-query-rule>
            - If the query contains **“and”**, keep it as a **single unified query**,  
              never break it into multiple searches.
        </single-query-rule>

        <examples>
            <example1>
                User: “provide the all conversation and content about the PSD?”  
                Delegation: “provide the all conversation and content about the PSD?”
            </example1>

            <example2>
                User: “hello, how are you, find ruh company objectives”  
                Delegation: “find ruh company objectives”
            </example2>

            <example3>
                User: “company objectives and goals”  
                Delegation: “company objectives and goals”  
                (ONE query; keep “and”)
            </example3>
        </examples>

    </critical-delegation-rules>

    <workflow>
        1. **Interpret the user’s request**
            - Ask: “Is this about organizational knowledge, internal documents, or employee-specific work items?”

        2. **If yes → delegate to knowledge_base_agent**
            - Clean query by removing greetings
            - Do not alter semantic content
            - Keep “and” as part of one unified query

        3. **Let the agent handle identity resolution automatically**
            - For person-specific queries: resolves identity → searches contextually
            - For general queries: searches directly

        4. **Return retrieved information with source URLs**
            - Present results clearly and authoritatively

        5. **If the query is not internal**
            - Use reasoning or web search as appropriate
    </workflow>

    <best-practices>
        - **Prefer knowledge_base_agent over web search** for any internal organizational topic.
        - Always preserve semantic meaning while removing only conversational fluff.
        - Trust the agent’s ability to resolve identities and filter results intelligently.
        - Provide concise, actionable summaries of retrieved documents or issues.
        - Include source URLs for transparency.
    </best-practices>

</knowledge-base-usage-instructions>

"""
