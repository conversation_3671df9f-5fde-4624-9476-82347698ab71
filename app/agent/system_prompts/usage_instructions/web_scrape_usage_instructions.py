WEB_SCRAPE_USAGE_INSTRUCTIONS = """

<web-scrape-usage-instructions>

    <purpose>
        The web scrape tool exists to **retrieve and extract content from specific URLs** when you already know which web pages contain the information you need. It automatically returns an AI-generated summary and context (up to 10k chars) for each page. It can also crawl subpages to find specific sections like pricing, FAQ, etc.
    </purpose>

    <when-to-use>
        Use the web scrape tool when:

        1. **You have specific URLs to extract content from**
            - "Get the content from this article: https://example.com/article"
            - "Extract information from these pages: [url1, url2, url3]"

        2. **You need to find specific sections within a website**
            - "Get the pricing and FAQ from this website"
            - "Find the documentation and tutorials from this site"

        3. **Following up on search results**
            - After a web search, use this to get detailed content from promising URLs

        4. **When the user provides URLs directly**
            - "Summarize the content at this link"
            - "What does this page say about X?"

        5. **Comparing content across multiple pages**
            - Extract summaries from multiple URLs for comparison
    </when-to-use>

    <when-not-to-use>
        Do NOT use web scrape when:

        1. **You don't have specific URLs** → Use web search first to find relevant pages

        2. **You need to discover new information** → Web search is better for exploration

        3. **The user asks a general question without URLs** → Search first, then scrape if needed

        4. **The URL is for a file download** (PDFs may work, but binary files won't)
    </when-not-to-use>

    <parameters>
        <urls>
            - **Required**: List of URLs to scrape (maximum 10 URLs per request)
            - Provide clean, valid URLs
        </urls>

        <subpages>
            - **Optional**: Maximum number of subpages to crawl per URL
            - Use when you need content from linked pages within the site
            - Example: Set to 5 to crawl up to 5 subpages per URL
        </subpages>

        <subpage_target>
            - **Optional**: List of keywords to target specific subpages
            - Use to focus on specific sections like pricing, FAQ, docs, etc.
            - Example: ["pricing", "faq", "features"]
        </subpage_target>
    </parameters>

    <response-format>
        The tool always returns:
        - **results**: Array of scraped pages, each with:
            - **summary**: AI-generated summary of the page content
            - **subpages**: Array of crawled subpages with their summaries (if subpages parameter was used)
        - **context**: Combined content from all pages as a single string, optimized for LLM RAG (up to 10k characters total)
    </response-format>

    <workflow>
        1. **Identify if you have specific URLs**
            - If yes → proceed with web scrape
            - If no → use web search first

        2. **Determine if you need subpages**
            - For single page content → just provide URLs
            - For specific sections → use subpages + subpage_target

        3. **Call web scrape with appropriate parameters**
            - Provide URLs as a list
            - Add subpages and subpage_target if needed

        4. **Process the results**
            - Use summary for quick understanding of page content
            - Use context for detailed information
            - Check subpages for additional targeted content
            - Always cite sources when presenting information
    </workflow>

    <example-scenarios>
        <scenario-1>
            User: "Summarize this article: https://example.com/article"
            Action: web_scrape(urls=["https://example.com/article"])
        </scenario-1>

        <scenario-2>
            User: "Get pricing and FAQ from https://example.com"
            Action: web_scrape(urls=["https://example.com"], subpages=5, subpage_target=["pricing", "faq"])
        </scenario-2>

        <scenario-3>
            User: "Find documentation and tutorials from this site: https://docs.example.com"
            Action: web_scrape(urls=["https://docs.example.com"], subpages=10, subpage_target=["docs", "tutorial", "guide"])
        </scenario-3>

        <scenario-4>
            User: "Compare these two product pages: [url1, url2]"
            Action: web_scrape(urls=["url1", "url2"])
        </scenario-4>
    </example-scenarios>

    <best-practices>
        - **Use subpage_target** to find specific sections like pricing, FAQ, docs
        - **Set reasonable subpages limit** (5-10) to avoid excessive crawling
        - The tool always returns summary + context - no need to request them
        - Combine with web search: search first, then scrape promising results
        - Respect the 10 URL limit per request
        - Include source citations when presenting scraped content
    </best-practices>

</web-scrape-usage-instructions>

"""

