"""
Web Scrape tool, powered by Exa's API.

This tool retrieves content from specified URLs with optional highlights
for specific terms. It uses Exa's get_contents endpoint to scrape web pages.
"""

import json
import os
import logging
import time
from typing import Any, Dict, Annotated, Optional, List

import aiohttp
from dotenv import load_dotenv
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.types import Command
from opentelemetry.trace import SpanKind
from app.shared.config.constants import RequestTypeEnum, AgentToolEnum
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker
from app.utils.agent_redis_store import store_sources

load_dotenv()

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool description
WEB_SCRAPE_TOOL_DESCRIPTION = """
- Retrieves content from one or more URLs using Exa's scraping capabilities
- Always returns AI-generated summary and context (10k chars) for each page
- Supports crawling subpages with optional keyword targeting (e.g., "pricing", "faq")
- Use subpages parameter to crawl linked pages, subpage_target for specific sections
"""

EXA_CONTENTS_BASE_URL = os.getenv("AI_GATEWAY_BASE_URL", "")
EXA_API_KEY = os.getenv("EXA_API_KEY")
AI_GATEWAY_API_KEY = os.getenv("AI_GATEWAY_API_KEY")


def _format_single_result(result: Dict[str, Any]) -> Dict[str, Any]:
    """Format a single result (main page or subpage)."""
    formatted = {
        "id": result.get("id", ""),
        "title": result.get("title", ""),
        "url": result.get("url", ""),
        "publishedDate": result.get("publishedDate", ""),
    }

    if "summary" in result:
        formatted["summary"] = result["summary"]

    # Include subpages if present (recursively format each)
    if "subpages" in result and result["subpages"]:
        formatted["subpages"] = [
            _format_single_result(subpage) for subpage in result["subpages"]
        ]

    return formatted


def _format_scrape_response(data: Dict[str, Any], urls: List[str]) -> str:
    """
    Format the scrape response to structured output with essential fields.

    Args:
        data: Raw response data from Exa API
        urls: Original URLs requested

    Returns:
        JSON string with structured format containing urls, results, and context
    """
    if "results" in data:
        structured_response = {"urls": urls, "results": []}

        for result in data["results"]:
            formatted_result = _format_single_result(result)
            structured_response["results"].append(formatted_result)

        # Include top-level context (combined string of all page contents)
        if "context" in data:
            structured_response["context"] = data["context"]

        return json.dumps(structured_response, indent=2)
    else:
        return json.dumps(data, indent=2)


def get_web_scrape_tool(
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
):
    """
    Create a web scrape tool with user context headers.

    Args:
        user_id: User identifier
        conversation_id: Optional conversation identifier
        agent_id: Optional agent identifier
        organisation_id: Optional organisation identifier
    """

    @tool(
        AgentToolEnum.WEB_SCRAPE.value,
        description=WEB_SCRAPE_TOOL_DESCRIPTION,
        parse_docstring=True,
    )
    async def web_scrape_tool(
        urls: List[str],
        subpages: Optional[int] = None,
        subpage_target: Optional[List[str]] = None,
        tool_call_id: Annotated[str, InjectedToolCallId] = None,
        config: Annotated[RunnableConfig, "Configuration object"] = None,
    ) -> Command:
        """Scrape web content from URLs using Exa's API.

        Args:
            urls: List of URLs to scrape content from (max 10 URLs).
            subpages: Optional max number of subpages to crawl per URL (e.g., 5).
            subpage_target: Optional list of keywords to target subpages (e.g., ["pricing", "faq"]).
            tool_call_id: Injected tool call identifier for message tracking.
            config: Configuration object containing conversation_id and context.

        Returns:
            Command: Command with scraped content including summary and context.
        """
        start_time = time.time()
        metrics_manager = get_metrics_manager()

        # Extract conversation_id from config
        config_conversation_id = None
        if config and "configurable" in config:
            config_conversation_id = config["configurable"].get("conversation_id")

        with trace_operation(
            "web_scrape.get_contents",
            kind=SpanKind.CLIENT,
            attributes={
                "urls_count": len(urls) if urls else 0,
                "user_id": user_id or "unknown",
                "organisation_id": organisation_id or "unknown",
                "has_subpages": subpages is not None,
                "has_subpage_target": subpage_target is not None,
            },
        ) as span:
            try:
                # Validate URLs
                if not urls or len(urls) == 0:
                    return _create_error_response(
                        "URLs parameter is required and cannot be empty",
                        tool_call_id
                    )

                if len(urls) > 10:
                    return _create_error_response(
                        "Maximum 10 URLs allowed per request",
                        tool_call_id
                    )

                # Validate each URL
                for url in urls:
                    if not url or not url.strip():
                        return _create_error_response(
                            "Each URL must be a non-empty string",
                            tool_call_id
                        )

                api_key = EXA_API_KEY

                # Build payload for Exa contents endpoint
                # Always include summary and context for optimal LLM usage
                payload = {
                    "urls": urls,
                    "summary": True,
                    "context": {
                        "maxCharacters": 10000
                    }
                }

                # Add subpages crawling if requested
                if subpages is not None and subpages > 0:
                    payload["subpages"] = subpages

                # Add subpage target keywords if provided
                if subpage_target and len(subpage_target) > 0:
                    payload["subpageTarget"] = subpage_target

                # Build headers with user context
                headers = {
                    "x-api-key": api_key,
                    "Content-Type": "application/json",
                    "X-Source": "agent-platform",
                    "X-Request-Type": RequestTypeEnum.WEB_SCRAPE.value,
                    "X-Deduct-RCU": "true",
                }

                if AI_GATEWAY_API_KEY:
                    headers["X-Server-Auth"] = AI_GATEWAY_API_KEY

                if user_id:
                    headers["X-User-Id"] = user_id
                if organisation_id:
                    headers["X-Organisation-Id"] = organisation_id
                if agent_id:
                    headers["X-Agent-Id"] = agent_id
                if config_conversation_id:
                    headers["X-Conversation-Id"] = config_conversation_id

                api_url = f"{EXA_CONTENTS_BASE_URL}/exa/contents"

                logger.info(f"Making Exa contents request for {len(urls)} URLs")

                # Create external API tracking span
                with trace_operation(
                    "exa.get_contents",
                    kind=SpanKind.CLIENT,
                    attributes={
                        "external_api": "exa",
                        "api_endpoint": "/exa/contents",
                        "operation": "get_contents",
                        "peer.service": "exa-api",
                        "http.method": "POST",
                        "http.url": api_url,
                    },
                ) as api_span:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(api_url, json=payload, headers=headers) as response:
                            api_span.set_attribute("http.status_code", response.status)

                            if response.status != 200:
                                api_span.set_attribute("error", True)
                                error_text = await response.text()

                                duration_ms = (time.time() - start_time) * 1000
                                metrics_manager.record_external_api_call(
                                    api_name="exa",
                                    endpoint="/exa/contents",
                                    attributes={
                                        "status_code": str(response.status),
                                        "error": "true",
                                    },
                                )
                                metrics_manager.record_external_api_duration(
                                    duration_ms=duration_ms,
                                    api_name="exa",
                                    endpoint="/exa/contents",
                                    attributes={"status_code": str(response.status)},
                                )

                                return _create_error_response(
                                    f"API request failed with status {response.status}: {error_text}",
                                    tool_call_id
                                )

                            try:
                                data = await response.json()
                            except Exception as e:
                                api_span.set_attribute("error", True)
                                api_span.set_attribute("error.message", str(e))

                                exception_tracker.track_exception(
                                    e,
                                    severity="error",
                                    attributes={
                                        "component": "web_scrape_response_parsing",
                                        "urls_count": len(urls),
                                    },
                                )

                                return _create_error_response(
                                    f"Failed to parse API response: {str(e)}",
                                    tool_call_id
                                )

                            formatted_response = _format_scrape_response(data, urls)
                            response_dict = json.loads(formatted_response)
                            response_dict["isError"] = False

                            web_sources = [
                                {"title": result.get("title", ""), "url": result.get("url", "")}
                                for result in response_dict.get("results", [])
                            ]

                            api_span.set_attribute("results_count", len(web_sources))
                            span.set_attribute("results_count", len(web_sources))

                            duration_ms = (time.time() - start_time) * 1000
                            metrics_manager.record_external_api_call(
                                api_name="exa",
                                endpoint="/exa/contents",
                                attributes={
                                    "status_code": str(response.status),
                                    "operation": "get_contents",
                                },
                            )
                            metrics_manager.record_external_api_duration(
                                duration_ms=duration_ms,
                                api_name="exa",
                                endpoint="/exa/contents",
                                attributes={"status_code": str(response.status)},
                            )

                            metrics_manager.record_tool_execution(
                                tool_name="web_scrape",
                                duration_ms=duration_ms,
                                attributes={"results_count": len(web_sources)},
                            )

                            # Store sources in Redis
                            effective_conversation_id = config_conversation_id or conversation_id
                            if effective_conversation_id:
                                await store_sources(effective_conversation_id, "web", web_sources)

                            return Command(
                                update={
                                    "messages": [
                                        ToolMessage(
                                            json.dumps(response_dict), tool_call_id=tool_call_id
                                        )
                                    ],
                                }
                            )

            except aiohttp.ClientError as e:
                logger.error(f"Connection error in web_scrape: {str(e)}")
                span.set_attribute("error_type", "client_error")

                metrics_manager.record_external_api_error(
                    api_name="exa",
                    endpoint="/exa/contents",
                    error_type="ClientError",
                )
                metrics_manager.record_tool_error(
                    tool_name="web_scrape",
                    error_type="ClientError",
                )
                return _create_error_response(f"Connection error: {str(e)}", tool_call_id)

            except ValueError as e:
                logger.error(f"Value error in web_scrape: {str(e)}")
                span.set_attribute("error_type", "value_error")

                metrics_manager.record_external_api_error(
                    api_name="exa",
                    endpoint="/exa/contents",
                    error_type="ValueError",
                )
                metrics_manager.record_tool_error(
                    tool_name="web_scrape",
                    error_type="ValueError",
                )
                return _create_error_response(str(e), tool_call_id)

            except Exception as e:
                exception_tracker.track_exception(
                    e,
                    severity="error",
                    attributes={
                        "component": "web_scrape_tool",
                        "urls_count": len(urls) if urls else 0,
                    },
                )

                logger.error(f"Unexpected error in web_scrape: {str(e)}")

                metrics_manager.record_external_api_error(
                    api_name="exa",
                    endpoint="/exa/contents",
                    error_type=type(e).__name__,
                )
                metrics_manager.record_tool_error(
                    tool_name="web_scrape",
                    error_type=type(e).__name__,
                )
                span.set_attribute("error_type", "unexpected_error")
                return _create_error_response(f"Unexpected error: {str(e)}", tool_call_id)

    return web_scrape_tool


def _create_error_response(error_message: str, tool_call_id: str) -> Command:
    """Create a standardized error response Command."""
    return Command(
        update={
            "messages": [
                ToolMessage(
                    json.dumps({"error": error_message, "isError": True}),
                    tool_call_id=tool_call_id,
                )
            ]
        }
    )

