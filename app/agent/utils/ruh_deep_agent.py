"""
Custom RUH Deep Agent implementation.

This module provides a customized version of the deep agent that:
- Removes TodoListMiddleware (we have our own todo tools)
- Removes FilesystemMiddleware (not needed)
- Sets general_purpose_agent=False in SubAgentMiddleware
- Uses SummarizationMiddleware with 100k token limit (instead of 170k)
- Keeps messages_to_keep=6
"""

from collections.abc import Sequence
from typing import Any

from langchain.agents import create_agent
from langchain.agents.middleware.summarization import SummarizationMiddleware
from langchain.agents.middleware.types import AgentMiddleware
from langchain_anthropic.middleware import AnthropicPromptCachingMiddleware
from langchain_core.language_models import BaseChatModel
from langchain_core.tools import BaseTool
from langgraph.types import Checkpointer

from deepagents.middleware.subagents import (
    CompiledSubAgent,
    SubAgent,
    SubAgentMiddleware,
)
from deepagents.middleware.patch_tool_calls import PatchToolCallsMiddleware

# Base prompt to inform agent about available tools
BASE_AGENT_PROMPT = "In order to complete the objective that the user asks of you, you have access to a number of standard tools."


def create_ruh_deep_agent(
    model: BaseChatModel,
    tools: Sequence[BaseTool | Any] | None = None,
    *,
    system_prompt: str | None = None,
    subagents: list[SubAgent | CompiledSubAgent] | None = None,
    checkpointer: Checkpointer | None = None,
    middleware: Sequence[AgentMiddleware] | None = None,
    max_tokens_before_summary: int = 100000,
    messages_to_keep: int = 6,
):
    """
    Create a customized RUH deep agent.

    This is a customized version of create_deep_agent that:
    - Does NOT include TodoListMiddleware (we use our own todo tools)
    - Does NOT include FilesystemMiddleware
    - Sets general_purpose_agent=False in SubAgentMiddleware
    - Uses configurable SummarizationMiddleware (default: 100k tokens)

    Args:
        model: The LangChain chat model to use.
        tools: The tools the agent should have access to.
        system_prompt: Additional instructions for the agent (added to system prompt).
        subagents: List of subagents for task delegation.
        checkpointer: Optional checkpointer for persisting agent state.
        middleware: Additional middleware to apply after standard middleware.
        max_tokens_before_summary: Token limit before triggering summarization (default: 100000).
        messages_to_keep: Number of recent messages to keep after summarization (default: 6).

    Returns:
        A configured deep agent (CompiledStateGraph).
    """
    # Define default middleware for subagents
    default_subagent_middleware = [
        SummarizationMiddleware(
            model=model,
            max_tokens_before_summary=max_tokens_before_summary,
            messages_to_keep=messages_to_keep,
        ),
        AnthropicPromptCachingMiddleware(unsupported_model_behavior="ignore"),
        PatchToolCallsMiddleware(),
    ]

    # Build the main middleware stack
    ruh_middleware: list[AgentMiddleware] = [
        # SubAgentMiddleware for handling subagent delegation
        SubAgentMiddleware(
            default_model=model,
            default_tools=tools,
            subagents=subagents if subagents is not None else [],
            default_middleware=default_subagent_middleware,
            default_interrupt_on=None,
            general_purpose_agent=False,  # Disabled - we don't want generic task spawning
        ),
        # SummarizationMiddleware for context management
        SummarizationMiddleware(
            model=model,
            max_tokens_before_summary=max_tokens_before_summary,
            messages_to_keep=messages_to_keep,
        ),
        # Anthropic-specific prompt caching optimization
        AnthropicPromptCachingMiddleware(unsupported_model_behavior="ignore"),
        # Patch tool calls for compatibility
        PatchToolCallsMiddleware(),
    ]

    # Add any additional custom middleware
    if middleware:
        ruh_middleware.extend(middleware)

    # Build final system prompt
    final_prompt = (
        system_prompt + "\n\n" + BASE_AGENT_PROMPT
        if system_prompt
        else BASE_AGENT_PROMPT
    )

    # Create and return the agent with high recursion limit for complex tasks
    return create_agent(
        model,
        system_prompt=final_prompt,
        tools=tools,
        middleware=ruh_middleware,
        checkpointer=checkpointer,
    ).with_config({"recursion_limit": 1000})
