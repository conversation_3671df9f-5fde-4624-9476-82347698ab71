from typing import Optional
import logging

from app.agent.utils.ruh_deep_agent import create_ruh_deep_agent
from app.agent.model import get_chat_model
from app.utils.tracing import trace_operation
from app.agent.subagents import (
    create_knowledge_base_subagent,
    create_web_search_subagent,
    create_mcp_subagent,
    # create_workflow_subagent,  # TEMPORARILY DISABLED - uncomment to re-enable
)
from app.agent.system_prompts.global_agent_prompt import get_global_agent_prompt
from app.agent.system_prompts.agent_prompt import get_agent_prompt
from app.agent.tools.todo import write_todos, read_todos
from app.agent.tools.read_file_tool import read_file
from app.agent.tools.mcp_search import mcp_search
from app.agent.tools.mcp_tool import execute_mcp
# from app.agent.tools.workflow_execution import workflow_execution  # TEMPORARILY DISABLED - uncomment to re-enable
from app.agent.tools import memory_tools
from app.agent.utils.mcp_utils import build_mcp_supervisor_prompt
# from app.agent.utils.workflow_utils import build_workflow_supervisor_prompt  # TEMPORARILY DISABLED - uncomment to re-enable
from app.services.agent_fetch import get_agent_fetch_service
from app.services.mongodb_client import get_mongodb_saver
from app.shared.config.constants import RequestTypeEnum
from app.helper.datetime_helper import get_current_datetime

logger = logging.getLogger(__name__)


async def create_deep_agent(
    provider: str,
    model_name: str,
    use_knowledge: bool,
    use_search: bool,
    user_id: str,
    organisation_id: str,
    conversation_id: str,
    agent_id: Optional[str] = None,
    use_memory: bool = True,
    mcp_ids: Optional[list] = None,
    use_thinking: bool = False,
    is_global: bool = True,
    timezone: str = "UTC",
    kb_source: Optional[str] = None,
    user_name: Optional[str] = None,
    user_email: Optional[str] = None,
    kb_file_ids: Optional[list] = None,
):
    """
    Create and configure a DeepAgent instance with the specified capabilities.

    Args:
        provider: The LLM provider to use
        model_name: The specific model name
        use_knowledge: Whether to enable knowledge base agent
        use_search: Whether to enable web search agent
        user_id: User identifier
        organisation_id: Organization identifier
        conversation_id: Conversation identifier
        agent_id: Optional agent identifier (required when is_global=False)
        use_memory: Whether to enable memory tools
        mcp_ids: Optional list of MCP tool IDs
        use_thinking: Whether to enable thinking mode
        is_global: Whether to use global agent prompt (vs regular agent prompt)
        timezone: User's timezone
        kb_source: Knowledge base source to search (e.g., "All", "Gmail", "Confluence", etc.)

    Returns:
        Compiled DeepAgent instance
    
    Raises:
        ValueError: If is_global=False and agent_id is not provided
    """
    # Validate required parameters
    if not is_global and not agent_id:
        raise ValueError("agent_id is required for non-global agents (is_global=False)")

    with trace_operation(
        "deepagent.init",
        attributes={
            "provider": provider,
            "model_name": model_name,
            "user_id": user_id,
            "organisation_id": organisation_id,
            "conversation_id": conversation_id,
            "agent_id": agent_id or "default",
            "use_knowledge": use_knowledge,
            "use_search": use_search,
            "use_memory": use_memory,
            "use_thinking": use_thinking,
            "is_global": is_global,
            "has_mcp_tools": bool(mcp_ids and len(mcp_ids) > 0),
        },
    ):
        with trace_operation("model.init"):
            model = get_chat_model(
                provider=provider,
                model_name=model_name,
                user_id=user_id,
                organisation_id=organisation_id,
                request_type=RequestTypeEnum.CHAT.value,
                agent_id=agent_id or "",
                conversation_id=conversation_id,
                use_thinking=use_thinking,
            )

            if hasattr(model, "bind"):
                model = model.bind(strict=True)

        subagents = []

        # Build dynamic agent prompt based on enabled features and is_global flag
        has_mcp_tools = bool(mcp_ids and len(mcp_ids) > 0)
        
        # Variables to store MCPs and workflows fetched from agent config (for non-global agents)
        mcps_from_agent_config = None
        # workflows_from_agent_config = None  # TEMPORARILY DISABLED - uncomment to re-enable

        if is_global:
            current_datetime = get_current_datetime(timezone)
            logger.info(f"[GLOBAL AGENT] Current datetime for prompt: {current_datetime} (Timezone: {timezone})")
            instructions = get_global_agent_prompt(
                use_knowledge=use_knowledge,
                use_search=use_search,
                use_memory=use_memory,
                has_mcp_tools=has_mcp_tools,
                current_datetime=current_datetime,
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id if agent_id else "",
                organisation_id=organisation_id,
                timezone=timezone,
                user_name=user_name or "",
                user_email=user_email or "",
                kb_file_ids=kb_file_ids if kb_file_ids else [],
            )
        else:
            # Fetch agent configuration for non-global agents
            # Note: agent_id is validated at function entry
            agent_fetch_service = get_agent_fetch_service()
            with trace_operation("agent.config.fetch", attributes={"agent_id": agent_id}):
                agent_config = await agent_fetch_service.fetch_agent_config(agent_id)
            
            # Extract agent information (extract only what we need to minimize memory)
            agent_name = agent_config.get("name", "AI Agent")
            agent_description = agent_config.get("description", "")
            system_message = agent_config.get("system_message", "")
            tone = agent_config.get("tone", "professional")
            agent_topic_type = agent_config.get("agent_topic_type", "")
            
            # Get MCPs from agent config if not already provided
            if not mcp_ids:
                agent_mcp_ids = agent_config.get("mcp_server_ids", [])
                if agent_mcp_ids:
                    mcp_ids = agent_mcp_ids
                    has_mcp_tools = True
            
            # Also extract MCPs and workflows data directly from agent config if available
            mcps_from_agent_config = agent_config.get("mcps", [])
            # workflows_from_agent_config = agent_config.get("workflows", [])  # TEMPORARILY DISABLED - uncomment to re-enable
            
            # Clear agent_config to free memory after extraction
            del agent_config
            
            # Check if workflows are available
            # has_workflows = bool(workflows_from_agent_config and len(workflows_from_agent_config) > 0)  # TEMPORARILY DISABLED - uncomment to re-enable
            has_workflows = False  # TEMPORARILY DISABLED - set back to above line to re-enable
            
            current_datetime = get_current_datetime(timezone)
            logger.info(f"[NON-GLOBAL AGENT] Current datetime for prompt: {current_datetime} (Timezone: {timezone}, Agent: {agent_name})")
            instructions = get_agent_prompt(
                use_knowledge=use_knowledge,
                use_search=use_search,
                use_memory=use_memory,
                has_mcp_tools=has_mcp_tools,
                has_workflows=has_workflows,
                agent_name=agent_name,
                agent_description=agent_description,
                system_message=system_message,
                tone=tone,
                agent_topic_type=agent_topic_type,
                current_datetime=current_datetime,
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id if agent_id else "",
                organisation_id=organisation_id,
                timezone=timezone,
                user_name=user_name or "",
                user_email=user_email or "",
                kb_file_ids=kb_file_ids if kb_file_ids else [],
            )
            
            # Clear extracted values after use to free memory
            del agent_name, agent_description, system_message, tone, agent_topic_type

        if use_knowledge:
            knowledge_base_subagent = create_knowledge_base_subagent(
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id if agent_id else "",
                organisation_id=organisation_id,
                timezone=timezone,
                kb_source=kb_source or "All",
                user_name=user_name or "",
                user_email=user_email or "",
                kb_file_ids=kb_file_ids if kb_file_ids else [],
            )
            subagents.append(knowledge_base_subagent)

        if use_search:
            web_search_subagent = create_web_search_subagent(
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id if agent_id else "",
                organisation_id=organisation_id,
                timezone=timezone,
                user_name=user_name or "",
                user_email=user_email or "",
            )
            subagents.append(web_search_subagent)

        if mcp_ids and len(mcp_ids) > 0:
            # For non-global agents, use MCPs from agent config if available
            mcps_data = None
            if not is_global and mcps_from_agent_config:
                mcps_data = mcps_from_agent_config
                # Clear the config reference since we're using it
                mcps_from_agent_config = None
            else:
                # Fetch MCPs by IDs
                if not is_global:
                    # agent_fetch_service already exists from earlier
                    pass
                else:
                    agent_fetch_service = get_agent_fetch_service()
                with trace_operation("mcp.fetch", attributes={"mcp_count": len(mcp_ids)}):
                    mcps_data = await agent_fetch_service.fetch_mcps_by_ids(mcp_ids)

            if mcps_data:
                mcp_info_for_supervisor = build_mcp_supervisor_prompt(mcps_data)

                # Add detailed MCP tools information to agent instructions
                instructions += (
                    "\n\n"
                    + "=" * 80
                    + "\n\n"
                    + "# AVAILABLE MCP TOOLS DETAILS\n\n"
                    + mcp_info_for_supervisor
                )

                mcp_subagent = create_mcp_subagent(
                    mcps_data=mcps_data,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    agent_id=agent_id if agent_id else "",
                    organisation_id=organisation_id,
                    timezone=timezone,
                    user_name=user_name or "",
                    user_email=user_email or "",
                )
                subagents.append(mcp_subagent)
                
                # Clear MCP data after use to free memory
                del mcps_data, mcp_info_for_supervisor

        # Handle workflows for non-global agents
        # TEMPORARILY DISABLED - uncomment this entire block to re-enable workflow subagent
        """
        if not is_global:
            # Get workflows from agent config if available
            workflows_data = None
            if workflows_from_agent_config:
                workflows_data = workflows_from_agent_config
                # Clear the config reference since we're using it
                workflows_from_agent_config = None
            
            if workflows_data and len(workflows_data) > 0:
                workflow_info_for_supervisor = build_workflow_supervisor_prompt(workflows_data)
                
                # Add detailed workflow information to agent instructions
                instructions += (
                    "\n\n"
                    + "=" * 80
                    + "\n\n"
                    + "# AVAILABLE WORKFLOWS DETAILS\n\n"
                    + workflow_info_for_supervisor
                )
                
                workflow_subagent = create_workflow_subagent(
                    workflows_data=workflows_data,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    agent_id=agent_id if agent_id else "",
                    organisation_id=organisation_id,
                    timezone=timezone,
                    user_name=user_name or "",
                    user_email=user_email or "",
                )
                subagents.append(workflow_subagent)
                
                # Clear workflow data after use to free memory
                del workflows_data, workflow_info_for_supervisor
        """

        # Conditionally add tools based on is_global flag
        if is_global:
            tools = [write_todos, read_todos, read_file, mcp_search]
        else:
            tools = [write_todos, read_todos, read_file]

        if use_memory:
            add_memory_tool = memory_tools.get_store_memory_tool(
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                organisation_id=organisation_id,
            )

            get_memory_tool = memory_tools.get_retrieve_memories_tool(
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                organisation_id=organisation_id,
            )

            tools.extend([get_memory_tool, add_memory_tool])

        with trace_operation("mongodb.init"):
            checkpointer = get_mongodb_saver()

        try:
            with trace_operation("mongodb.state.get"):
                existing_state = checkpointer.get(
                    {
                        "configurable": {
                            "thread_id": conversation_id,
                            "user_id": user_id,
                            "organisation_id": organisation_id,
                        }
                    }
                )

            if existing_state and existing_state.values.get("summary"):
                summary = existing_state.values.get("summary")
                instructions += (
                    "\n\n"
                    + "=" * 80
                    + "\n\n"
                    + "# CONVERSATION SUMMARY\n"
                    + f"Use this summary for context about previous conversation:\n\n{summary}\n"
                )
        except Exception:
            pass

        with trace_operation("deepagent.create"):
            agent = create_ruh_deep_agent(
                model=model,
                tools=tools,
                system_prompt=instructions,
                subagents=subagents,
                checkpointer=checkpointer,
            )

        return agent
