"""Utilities for conversation summarization."""

import logging
from typing import Optional
from langchain_core.messages import HumanMessage

from app.shared.config.constants import RequestTypeEnum
from app.agent.system_prompts.summarization import SUMMARI<PERSON><PERSON>ION_PROMPT
from app.utils.tracing import trace_operation
from app.helper.logging_helper import log_structured_data


def _get_messages_preserving_tool_pairs(
    messages: list, min_messages_to_keep: int = 5
) -> list:
    """
    Intelligently keep messages while preserving tool_use/tool_result pairs.

    Strategy:
    1. Work backwards from the end
    2. Keep the last N messages and their tool pairs
    3. For any tool_result, also keep its corresponding tool_use

    Args:
        messages: List of all messages
        min_messages_to_keep: Minimum number of recent messages to preserve

    Returns:
        List of messages to keep
    """
    if len(messages) <= min_messages_to_keep:
        return messages

    messages_to_keep_indices = set(
        range(max(0, len(messages) - min_messages_to_keep), len(messages))
    )

    tool_call_ids = {}
    for i, msg in enumerate(messages):
        msg_type = msg.type if hasattr(msg, "type") else None

        if msg_type == "tool":
            if hasattr(msg, "tool_call_id"):
                tool_call_ids[msg.tool_call_id] = i

    for idx in list(messages_to_keep_indices):
        msg = messages[idx]
        msg_type = msg.type if hasattr(msg, "type") else None

        if msg_type == "ai" and hasattr(msg, "tool_calls") and msg.tool_calls:
            for tool_call in msg.tool_calls:
                tool_id = tool_call.id if hasattr(tool_call, "id") else None
                if tool_id and tool_id in tool_call_ids:
                    tool_result_idx = tool_call_ids[tool_id]
                    messages_to_keep_indices.add(tool_result_idx)

    sorted_indices = sorted(messages_to_keep_indices)
    return [messages[i] for i in sorted_indices]


async def check_and_summarize(
    supervisor,
    conversation_id: str,
    user_id: str,
    organisation_id: str,
    agent_id: Optional[str] = None,
    total_tokens: int = 0,
    token_threshold: int = 100000,
):
    """
    Single function that checks token count and summarizes if threshold exceeded.
    Does both checking and summarization in one call.

    Usage:
        total_tokens = 0
        async for chunk in run_global_stream(...):
            if chunk.get("type") == "message_end" and chunk.get("usage_data"):
                total_tokens += chunk["usage_data"].get("total_tokens", 0)
            yield chunk

        result = await check_and_summarize(
            supervisor=supervisor,
            conversation_id=conversation_id,
            user_id=user_id,
            organisation_id=organisation_id,
            total_tokens=total_tokens,
        )

    Args:
        supervisor: The compiled supervisor graph
        conversation_id: Conversation/thread ID
        user_id: User ID
        organisation_id: Organization ID
        agent_id: Agent ID (optional)
        total_tokens: Actual cumulative token count from usage_data in stream
        token_threshold: Token count must exceed this to trigger summarization (default: 50000)

    Returns:
        Dict with result (either summarization complete or check status)
    """
    with trace_operation(
        "summarization.check_and_summarize",
        attributes={
            "user_id": user_id,
            "organisation_id": organisation_id,
            "conversation_id": conversation_id,
            "agent_id": agent_id or "default",
            "total_tokens": total_tokens,
            "token_threshold": token_threshold,
        },
    ) as span:
        from app.agent.model import get_chat_model
        from app.services.redis_streams import get_redis_manager

        # Get state snapshot
        state_snapshot = supervisor.get_state(
            config={
                "configurable": {
                    "thread_id": conversation_id,
                    "user_id": user_id,
                    "organisation_id": organisation_id,
                }
            }
        )

        state_values = state_snapshot.values
        messages = state_values.get("messages", [])
        message_count = len(messages)

        # Check if summarization is needed based on actual token count from stream
        should_summarize = total_tokens > token_threshold

        # Log summarization check in table format
        check_data = {
            "total_tokens": total_tokens,
            "threshold": token_threshold,
            "should_summarize": should_summarize,
            "message_count": message_count,
        }
        log_structured_data(logging.getLogger(__name__), "SUMMARIZATION CHECK", check_data, level="info")

        if should_summarize:
            summary = state_values.get("summary", "")

            # Get model and provider from settings
            from app.shared.config.base import get_settings

            settings = get_settings()

            model = get_chat_model(
                provider=settings.summarisation.provider,
                model_name=settings.summarisation.model,
                user_id=user_id,
                organisation_id=organisation_id,
                request_type=RequestTypeEnum.CHAT_SUMMARIZATION.value,
                agent_id=agent_id or "",
                conversation_id=conversation_id,
            )

            if summary:
                summary_message = SUMMARIZATION_PROMPT.format(
                    existing_summary=f"This is a summary of the conversation to date:\n{summary}\n\nExtend the summary by taking into account the new messages above:"
                )
            else:
                summary_message = SUMMARIZATION_PROMPT.format(
                    existing_summary="Create a summary of the conversation above:"
                )

            messages_for_summary = messages + [HumanMessage(content=summary_message)]

            response = await model.ainvoke(messages_for_summary)
            new_summary = response.content

            # Extract token usage from response
            input_tokens = 0
            output_tokens = 0

            if hasattr(response, "usage_metadata") and response.usage_metadata:
                usage = response.usage_metadata
                input_tokens = usage.get("input_tokens", 0)
                output_tokens = usage.get("output_tokens", 0)
                # Log usage data in table format
                usage_data = {
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "total_tokens": input_tokens + output_tokens,
                }
                log_structured_data(logging.getLogger(__name__), "SUMMARIZATION USAGE", usage_data, level="info")

            kept_messages = _get_messages_preserving_tool_pairs(
                messages, min_messages_to_keep=5
            )

            supervisor.update_state(
                config={
                    "configurable": {
                        "thread_id": conversation_id,
                        "user_id": user_id,
                        "organisation_id": organisation_id,
                    }
                },
                values={
                    "summary": new_summary,
                    "messages": kept_messages,
                },
            )

            summary_preview = (
                new_summary[:200] + "..." if len(new_summary) > 200 else new_summary
            )

            # Send summarization data to Redis streams
            try:
                redis_manager = await get_redis_manager()

                stream_name = f"{settings.environment}:summarisation:save"

                await redis_manager.producer.send_message(
                    stream=stream_name,
                    fields={
                        "user_id": user_id,
                        "conversation_id": conversation_id,
                        "summary": new_summary,
                        "input_tokens": str(input_tokens),
                        "output_tokens": str(output_tokens),
                    },
                )

                # Log successful summarization save in table format
                summary_save_data = {
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "stream_name": stream_name,
                    "summary_length": len(new_summary),
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                }
                log_structured_data(logging.getLogger(__name__), "SUMMARIZATION SAVED", summary_save_data, level="info")
            except Exception as e:
                logging.error(f"Failed to send summarization data to Redis: {e}")

            span.set_attribute("summarized", True)
            span.set_attribute("message_count_before", message_count)
            span.set_attribute("message_count_after", len(kept_messages))
            span.set_attribute("summary_length", len(new_summary))

            return {
                "type": "summarization_complete",
                "data": {
                    "summarized": True,
                    "message_count_before": message_count,
                    "message_count_after": len(kept_messages),
                    "total_tokens_before": total_tokens,
                    "summary_length": len(new_summary),
                    "summary_preview": summary_preview,
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "reason": f"Tokens: {total_tokens} exceeded threshold: {token_threshold}",
                },
            }
        else:
            return {
                "type": "summarization_check",
                "data": {
                    "summarized": False,
                    "message_count": message_count,
                    "total_tokens": total_tokens,
                    "token_threshold": token_threshold,
                    "reason": f"Tokens ({total_tokens}) below threshold ({token_threshold})",
                },
            }
