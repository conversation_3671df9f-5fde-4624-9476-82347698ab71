"""
Workflow Subagent for DeepAgent architecture.
"""

from typing import Optional, List
from deepagents.middleware.subagents import SubAgent
from app.agent.tools.workflow_execution import workflow_execution
from app.agent.system_prompts.workflow_subagent import WORKFLOW_SUBAGENT_PROMPT
from app.agent.system_prompts.usage_instructions.general_agent_instructions import (
    GENERAL_SUBAGENT_INSTRUCTIONS,
)
from app.agent.utils.workflow_utils import build_workflow_subagent_prompt
from app.helper.datetime_helper import get_current_datetime


def create_workflow_subagent(
    workflows_data: Optional[List] = None,
    user_id: str = "",
    conversation_id: str = "",
    agent_id: str = "",
    organisation_id: str = "",
    timezone: str = "UTC",
    user_name: str = "",
    user_email: str = "",
) -> SubAgent:
    """
    Create a workflow subagent for DeepAgent.

    Args:
        workflows_data: Optional list of workflow configurations
        user_id: User identifier
        conversation_id: Conversation identifier
        agent_id: Agent identifier
        organisation_id: Organisation identifier
        timezone: User's timezone

    Returns:
        SubAgent configuration dictionary with name, description, system_prompt, and tools.
    """
    # Format general subagent instructions
    formatted_general_instructions = GENERAL_SUBAGENT_INSTRUCTIONS.format(
        datetime=get_current_datetime(timezone),
        user_id=user_id or "N/A",
        user_name=user_name or "N/A",
        user_email=user_email or "N/A",
        conversation_id=conversation_id or "N/A",
        agent_id=agent_id or "N/A",
        organisation_id=organisation_id or "N/A",
        timezone=timezone,
        kb_file_ids="None",
        kb_file_context="",
    )
    
    # No formatting needed - WORKFLOW_SUBAGENT_PROMPT has no placeholders
    formatted_workflow_base_prompt = WORKFLOW_SUBAGENT_PROMPT

    # Combine base prompt with dynamic workflow tool details if provided
    if workflows_data:
        dynamic_workflow_tools = build_workflow_subagent_prompt(workflows_data)
        workflow_subagent_prompt = formatted_workflow_base_prompt + "\n\n" + dynamic_workflow_tools
    else:
        workflow_subagent_prompt = formatted_workflow_base_prompt
    
    # Prepend general instructions to the full workflow prompt
    full_prompt = formatted_general_instructions + "\n\n" + workflow_subagent_prompt

    return {
        "name": "workflow_subagent",
        "description": "Used to execute workflow orchestrations and automations",
        "system_prompt": full_prompt,
        "tools": [workflow_execution],
    }
