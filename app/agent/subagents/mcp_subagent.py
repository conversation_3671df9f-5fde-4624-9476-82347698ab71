"""
MCP Subagent for DeepAgent architecture.
"""

from typing import Optional, List
from deepagents.middleware.subagents import SubAgent
from app.agent.tools.mcp_tool import execute_mcp
from app.agent.system_prompts.mcp_subagent import MCP_SUBAGENT_PROMPT
from app.agent.system_prompts.usage_instructions.general_agent_instructions import (
    GENERAL_SUBAGENT_INSTRUCTIONS,
)
from app.agent.utils.mcp_utils import build_mcp_subagent_prompt
from app.helper.datetime_helper import get_current_datetime


def create_mcp_subagent(
    mcps_data: Optional[List] = None,
    user_id: str = "",
    conversation_id: str = "",
    agent_id: str = "",
    organisation_id: str = "",
    timezone: str = "UTC",
    user_name: str = "",
    user_email: str = "",
) -> SubAgent:
    """
    Create an MCP subagent for DeepAgent.

    Args:
        mcps_data: Optional list of MCP configurations
        user_id: User identifier
        conversation_id: Conversation identifier
        agent_id: Agent identifier
        organisation_id: Organisation identifier
        timezone: User's timezone

    Returns:
        SubAgent configuration dictionary with name, description, system_prompt, and tools.
    """
    # Format general subagent instructions
    formatted_general_instructions = GENERAL_SUBAGENT_INSTRUCTIONS.format(
        datetime=get_current_datetime(timezone),
        user_id=user_id or "N/A",
        user_name=user_name or "N/A",
        user_email=user_email or "N/A",
        conversation_id=conversation_id or "N/A",
        agent_id=agent_id or "N/A (Global Agent)",
        organisation_id=organisation_id or "N/A",
        timezone=timezone,
        kb_file_ids="None",
        kb_file_context="",
    )

    # No formatting needed - MCP_SUBAGENT_PROMPT has no placeholders
    formatted_mcp_base_prompt = MCP_SUBAGENT_PROMPT

    # Combine base prompt with dynamic MCP tool details if provided
    if mcps_data:
        dynamic_mcp_tools = build_mcp_subagent_prompt(mcps_data)
        mcp_subagent_prompt = formatted_mcp_base_prompt + "\n\n" + dynamic_mcp_tools
    else:
        mcp_subagent_prompt = formatted_mcp_base_prompt

    # Prepend general instructions to the full MCP prompt
    full_prompt = formatted_general_instructions + "\n\n" + mcp_subagent_prompt

    return {
        "name": "tool_executor_subagent",
        "description": "Used to execute MCP (Model Context Protocol) tools and services",
        "system_prompt": full_prompt,
        "tools": [execute_mcp],
    }
