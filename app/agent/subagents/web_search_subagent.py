"""
Web Search Subagent for DeepAgent architecture.
"""

from typing import Optional
from deepagents.middleware.subagents import SubAgent
from app.agent.tools.web_search_tool import get_web_search_tool
from app.agent.tools.web_scrape_tool import get_web_scrape_tool
from app.agent.system_prompts.web_search_subagent import WEB_SEARCH_SUBAGENT_PROMPT
from app.agent.system_prompts.usage_instructions.general_agent_instructions import (
    GENERAL_SUBAGENT_INSTRUCTIONS,
)
from app.helper.datetime_helper import get_current_datetime


def create_web_search_subagent(
    user_id: str = "",
    conversation_id: str = "",
    agent_id: str = "",
    organisation_id: str = "",
    timezone: str = "UTC",
    user_name: str = "",
    user_email: str = "",
) -> SubAgent:
    """
    Create a web search subagent for DeepAgent.

    Args:
        user_id: User identifier
        conversation_id: Conversation identifier
        agent_id: Agent identifier
        organisation_id: Organisation identifier
        timezone: User's timezone

    Returns:
        SubAgent configuration dictionary with name, description, system_prompt, and tools.
    """
    # Create web search tool with user context
    web_search_tool = get_web_search_tool(
        user_id=user_id,
        conversation_id=conversation_id,
        agent_id=agent_id,
        organisation_id=organisation_id,
    )

    # Create web scrape tool with user context
    web_scrape_tool = get_web_scrape_tool(
        user_id=user_id,
        conversation_id=conversation_id,
        agent_id=agent_id,
        organisation_id=organisation_id,
    )

    # Format general subagent instructions
    formatted_general_instructions = GENERAL_SUBAGENT_INSTRUCTIONS.format(
        datetime=get_current_datetime(timezone),
        user_id=user_id or "N/A",
        user_name=user_name or "N/A",
        user_email=user_email or "N/A",
        conversation_id=conversation_id or "N/A",
        agent_id=agent_id or "N/A (Global Agent)",
        organisation_id=organisation_id or "N/A",
        timezone=timezone,
        kb_file_ids="None",
        kb_file_context="",
    )
    
    # No formatting needed - WEB_SEARCH_SUBAGENT_PROMPT has no placeholders
    formatted_web_search_prompt = WEB_SEARCH_SUBAGENT_PROMPT
    
    # Combine general instructions with specific subagent prompt
    full_prompt = formatted_general_instructions + "\n\n" + formatted_web_search_prompt

    return {
        "name": "web_search_agent",
        "description": "Used to search the web for information or scrape content from specific URLs",
        "system_prompt": full_prompt,
        "tools": [web_search_tool, web_scrape_tool],
    }
