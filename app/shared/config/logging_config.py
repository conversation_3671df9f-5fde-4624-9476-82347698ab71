import json
import logging.config
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

try:
    from opentelemetry import trace

    OTEL_AVAILABLE = True
except ImportError:
    OTEL_AVAILABLE = False


def add_trace_context_to_record(record_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add OpenTelemetry trace context to log record.

    Args:
        record_dict: Dictionary containing log record data

    Returns:
        Updated record dictionary with trace context if available
    """
    if OTEL_AVAILABLE:
        try:
            span = trace.get_current_span()
            if span and span.get_span_context().is_valid:
                ctx = span.get_span_context()
                record_dict["trace_id"] = format(ctx.trace_id, "032x")
                record_dict["span_id"] = format(ctx.span_id, "016x")
                record_dict["trace_flags"] = ctx.trace_flags
        except Exception:
            # Silently ignore any errors in trace context extraction
            pass
    return record_dict


class JSONFormatter(logging.Formatter):
    """
    Formatter that outputs JSON strings after parsing the log record.
    Automatically includes OpenTelemetry trace context when available.
    """

    def __init__(
        self,
        fmt_dict: Optional[Dict[str, Any]] = None,
        include_trace_context: bool = True,
    ):
        self.fmt_dict = (
            fmt_dict
            if fmt_dict
            else {
                "timestamp": "%(asctime)s",
                "level": "%(levelname)s",
                "name": "%(name)s",
                "message": "%(message)s",
            }
        )
        self.include_trace_context = include_trace_context
        super().__init__()

    def format(self, record: logging.LogRecord) -> str:
        record_dict = {}
        for key, value in self.fmt_dict.items():
            if key == "timestamp":
                record_dict[key] = datetime.fromtimestamp(record.created).isoformat()
            elif key == "message" and hasattr(record, "json_message"):
                # If the record has a json_message attribute, use it instead of the message
                record_dict[key] = record.json_message
            else:
                record_dict[key] = value % record.__dict__

        # Add OpenTelemetry trace context
        if self.include_trace_context:
            record_dict = add_trace_context_to_record(record_dict)

        # Add exception info if available
        if record.exc_info:
            record_dict["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info),
            }

        # Add extra fields
        if hasattr(record, "extra") and isinstance(record.extra, dict):
            record_dict.update(record.extra)

        return json.dumps(record_dict)


def setup_logging(
    default_level: str = "INFO", logs_dir: str = "logs", use_json: bool = False
) -> None:
    """
    Configure logging for the application with OpenTelemetry trace correlation.

    When OpenTelemetry is available and active, logs will automatically include:
    - trace_id: Unique identifier for the distributed trace
    - span_id: Unique identifier for the current span
    - trace_flags: Trace sampling flags

    This enables automatic correlation between logs and traces in observability platforms
    like SigNoz, Jaeger, or other OpenTelemetry-compatible backends.

    Args:
        default_level: Default logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        logs_dir: Directory to store log files
        use_json: Whether to use JSON formatting for logs (recommended for trace correlation)
    """
    # Get log levels from environment variables
    # Format: LOG_LEVEL=INFO or LOG_LEVEL=DEBUG,INFO,WARNING,ERROR,CRITICAL
    env_level_str = os.getenv("LOG_LEVEL", default_level).upper()

    # Parse multiple log levels if provided
    log_levels = [level.strip() for level in env_level_str.split(",")]

    # Determine the effective log level (lowest/most verbose from the list)
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL,
    }

    # Default to INFO if invalid levels are provided
    effective_level = level_map.get(default_level.upper(), logging.INFO)

    # Find the lowest (most verbose) level in the provided list
    for level in log_levels:
        if level in level_map and level_map[level] < effective_level:
            effective_level = level_map[level]

    # Convert back to string representation for config
    env_level = logging.getLevelName(effective_level)

    # Log level is now determined directly in the loggers configuration

    # Ensure logs directory exists
    Path(logs_dir).mkdir(exist_ok=True)

    # Create formatters based on format preference
    if use_json:
        default_formatter = {
            "()": JSONFormatter,
            "fmt_dict": {
                "timestamp": "%(asctime)s",
                "level": "%(levelname)s",
                "name": "%(name)s",
                "message": "%(message)s",
                "process": "%(process)d",
                "thread": "%(thread)d",
            },
        }
        detailed_formatter = {
            "()": JSONFormatter,
            "fmt_dict": {
                "timestamp": "%(asctime)s",
                "level": "%(levelname)s",
                "name": "%(name)s",
                "message": "%(message)s",
                "process": "%(process)d",
                "thread": "%(thread)d",
                "file": "%(pathname)s",
                "line": "%(lineno)d",
                "function": "%(funcName)s",
            },
        }
    else:
        default_formatter = {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        }
        detailed_formatter = {
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
        }
    json_detailed_formatter = {
        "()": JSONFormatter,
        "fmt_dict": {
            "timestamp": "%(asctime)s",
            "level": "%(levelname)s",
            "name": "%(name)s",
            "message": "%(message)s",
            "process": "%(process)d",
            "thread": "%(thread)d",
            "file": "%(pathname)s",
            "line": "%(lineno)d",
            "function": "%(funcName)s",
        },
    }
    config = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": default_formatter,
            "detailed": detailed_formatter,
            "json_detailed": json_detailed_formatter,
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
            "app_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "detailed",
                "filename": os.path.join(logs_dir, "app.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
            "error_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "detailed",
                "filename": os.path.join(logs_dir, "errors.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "level": "ERROR",
            },
            "kafka_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "detailed",
                "filename": os.path.join(logs_dir, "kafka.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
            "agent_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "detailed",
                "filename": os.path.join(logs_dir, "agent.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
            "session_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "detailed",
                "filename": os.path.join(logs_dir, "session.log"),
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
            },
            "workflow_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "json_detailed",
                "filename": os.path.join(logs_dir, "workflow.log"),
                "maxBytes": 1073741824,  # 1GB
                "backupCount": 5,
            },
            "workflow_chat_file": {
                "class": "logging.handlers.RotatingFileHandler",
                "formatter": "json_detailed",
                "filename": os.path.join(logs_dir, "workflow_chat.log"),
                "maxBytes": 1073741824,  # 1GB
                "backupCount": 5,
            },
        },
        "loggers": {
            "": {  # Root logger
                "handlers": ["console", "app_file", "error_file"],
                "level": env_level,
                "propagate": True,
            },
            "app.kafka_client": {
                "handlers": ["console", "kafka_file", "error_file"],
                "level": os.getenv("KAFKA_LOG_LEVEL", env_level).upper(),
                "propagate": False,
            },
            "app.autogen_service": {
                "handlers": ["console", "agent_file", "error_file"],
                "level": os.getenv("AGENT_LOG_LEVEL", env_level).upper(),
                "propagate": False,
            },
            "app.helper.session_manager": {
                "handlers": ["console", "session_file", "error_file"],
                "level": os.getenv("SESSION_LOG_LEVEL", env_level).upper(),
                "propagate": False,
            },
            "app.services.redis_client": {
                "handlers": ["console", "app_file", "error_file"],
                "level": os.getenv("REDIS_LOG_LEVEL", env_level).upper(),
                "propagate": False,
            },
            "app.workflow_generation_graph.agent": {
                "handlers": ["console", "workflow_file", "error_file"],
                "level": "INFO",
                "propagate": False,
            },
            "app.workflow_chat.agent": {
                "handlers": ["console", "workflow_chat_file", "error_file"],
                "level": "INFO",
                "propagate": False,
            },
            # Add specific loggers for different log levels
            "debug": {
                "handlers": ["console", "app_file"],
                "level": "DEBUG",
                "propagate": False,
            },
            "info": {
                "handlers": ["console", "app_file"],
                "level": "INFO",
                "propagate": False,
            },
            "warning": {
                "handlers": ["console", "app_file"],
                "level": "WARNING",
                "propagate": False,
            },
            "error": {
                "handlers": ["console", "error_file"],
                "level": "ERROR",
                "propagate": False,
            },
            "critical": {
                "handlers": ["console", "error_file"],
                "level": "CRITICAL",
                "propagate": False,
            },
        },
    }

    # Apply configuration
    logging.config.dictConfig(config)

    # Log the configuration setup
    logger = logging.getLogger(__name__)
    otel_status = "enabled" if OTEL_AVAILABLE else "not available"
    logger.info(
        f"Logging configured with level: {env_level}, JSON format: {use_json}, "
        f"OpenTelemetry trace correlation: {otel_status}"
    )

    # Disable some noisy third-party loggers
    for logger_name in [
        "aiokafka",
        "kafka",
        "asyncio",
        "urllib3.connectionpool",
        "httpx",
        "httpcore",
        "apscheduler.scheduler",
        "apscheduler.executors",
        "mem0.vector_stores.qdrant",
        "qdrant_client",
    ]:
        logging.getLogger(logger_name).setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the given name.

    The logger automatically includes OpenTelemetry trace context in JSON formatted logs.

    Args:
        name: Logger name, typically __name__ of the calling module

    Returns:
        Configured logger instance with trace context support
    """
    return logging.getLogger(name)


def get_structured_logger(name: str) -> logging.Logger:
    """
    Get a structured logger with OpenTelemetry trace context support.

    This is an alias for get_logger() but makes it explicit that the logger
    supports structured logging with trace correlation.

    Args:
        name: Logger name, typically __name__ of the calling module

    Returns:
        Configured logger instance with trace context support

    Example:
        logger = get_structured_logger(__name__)
        logger.info(
            "User created campaign",
            extra={
                "user_id": "user-123",
                "campaign_id": "camp-456",
                "campaign_name": "Q4 Outreach"
            }
        )
    """
    return logging.getLogger(name)


def get_level_logger(level: str) -> logging.Logger:
    """
    Get a logger that only logs at the specified level.

    Args:
        level: Log level (debug, info, warning, error, critical)

    Returns:
        Logger that only logs at the specified level
    """
    level = level.lower()
    if level not in ["debug", "info", "warning", "error", "critical"]:
        level = "info"  # Default to info if invalid level

    return logging.getLogger(level)


def log_with_context(
    logger: logging.Logger,
    level: int,
    msg: str,
    extra: Optional[Dict[str, Any]] = None,
    log_to_levels: Optional[list] = None,
) -> None:
    """
    Log a message with additional context and OpenTelemetry trace correlation.

    This function automatically includes trace context (trace_id, span_id) when
    OpenTelemetry tracing is active. Additional context fields are added to the log.

    Args:
        logger: Logger instance
        level: Primary logging level (e.g., logging.INFO)
        msg: Log message
        extra: Additional context to include in the log (e.g., user_id, request_id)
        log_to_levels: Optional list of level names to also log to (e.g., ["debug", "info"])

    Example:
        log_with_context(
            logger,
            logging.INFO,
            "User action completed",
            extra={
                "user_id": "user-123",
                "action": "create_campaign",
                "duration_ms": 150
            }
        )
    """
    if extra is None:
        extra = {}

    # Log to the primary logger
    logger.log(level, msg, extra={"extra": extra})

    # Also log to specific level loggers if requested
    if log_to_levels:
        for level_name in log_to_levels:
            level_logger = get_level_logger(level_name)
            # Convert the int level to the corresponding level for this logger
            level_logger.log(level, msg, extra={"extra": extra})


def log_to_all_levels(msg: str, extra: Optional[Dict[str, Any]] = None) -> None:
    """
    Log a message to all log levels (debug, info, warning, error, critical).

    Automatically includes OpenTelemetry trace context when available.

    Args:
        msg: Log message
        extra: Additional context to include in the log
    """
    if extra is None:
        extra = {}

    for level_name in ["debug", "info", "warning", "error", "critical"]:
        level_logger = get_level_logger(level_name)
        # Use the appropriate level for each logger
        level_value = getattr(logging, level_name.upper())
        level_logger.log(level_value, msg, extra={"extra": extra})
