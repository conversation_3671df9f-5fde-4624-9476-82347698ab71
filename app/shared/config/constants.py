from enum import Enum


class RedisStreamEnum(str, Enum):
    """Redis Streams names for agent communication"""

    AGENT_REQUESTS = "{env}:agent:chat:requests"
    AGENT_RESPONSES = "{env}:agent:chat:responses:{conversation_id}"
    MEMORY_REQUESTS = "{env}:memory:requests"
    STOP_REQUESTS = "{env}:agent:stop:requests"


class RedisConsumerGroupEnum(str, Enum):
    """Redis Streams consumer group names"""

    AGENT_PROCESSORS = "agent-processors"
    MEMORY_PROCESSORS = "memory-processors"
    STOP_PROCESSORS = "stop-processors"


class RequestPlatforms(str, Enum):
    """Platforms for which the agent can be used"""

    WEB = "web"
    SMS = "sms"


class SSEEventType(str, Enum):
    STREAM_START = "stream_start"
    MESSAGE_START = "message_start"
    CHUNK = "chunk"
    TOOL_USE_START = "tool_use_start"
    TOOL_CHUNK = "tool_chunk"
    TOOL_RESULT = "tool_result"
    TEXT_RESULT = "text_result"
    STREAM_END = "stream_end"


class RedisWorkflowStreamEnum(str, Enum):
    """Redis Streams names for workflow communication"""

    WORKFLOW_REQUESTS = "{env}:workflow:requests"
    WORKFLOW_RESPONSES = "{env}:workflow:responses"

    WORKFLOW_CHAT_RESPONSES = "{env}:workflow:chat:responses"
    WORKFLOW_CHAT_REQUESTS = "{env}:workflow:chat:requests"


class RedisWorkflowConsumerGroupEnum(str, Enum):
    """Redis Streams consumer group names"""

    WORKFLOW_PROCESSORS = "workflow-processors"


class Workflow_SSEEventType(str, Enum):
    """Server Sent Event types for workflow generation"""

    STREAM_START = "stream_start"
    SUB_AGENT_CALLED = "sub_agent_called"
    MESSAGE = "message"
    TOOL_CALLED = "tool_called"
    PRE_PROCESSING = "pre_processing"
    POST_PROCESSING = "post_processing"
    WORKFLOW_GENERATED = "workflow_generated"
    ERROR = "error"
    STREAM_END = "stream_end"


class EventType(str, Enum):
    STREAM_START = "stream_start"
    STREAM_END = "stream_end"
    MESSAGE_START = "message_start"
    MESSAGE = "message"
    MESSAGE_END = "message_end"
    TOOL_START = "tool_start"
    TOOL_END = "tool_end"
    TOOL_STREAM = "tool_stream"
    THINKING_START = "thinking_start"
    THINKING = "thinking"
    THINKING_END = "thinking_end"


class RequestTypeEnum(str, Enum):
    """Request types for categorizing API calls to model providers"""

    CHAT = "chat"
    CHAT_SUMMARIZATION = "chat_summarization"
    MEM0_LLM = "mem0_llm"
    MEM0_EMBEDDING = "mem0_embedding"
    WEB_SEARCH = "web_search"
    WEB_SCRAPE = "web_scrape"


class KnowledgeSource(str, Enum):
    """Enumeration of knowledge sources for knowledge base queries."""

    ALL = "ALL"
    GOOGLE_DRIVE = "GOOGLE_DRIVE"
    JIRA = "JIRA"
    CONFLUENCE = "CONFLUENCE"
    GOOGLE_CALENDAR = "GOOGLE_CALENDAR"
    GITHUB = "GITHUB"
    GMAIL = "GMAIL"


class AgentToolEnum(str, Enum):
    """Agent tool names used in the global agent"""

    # Create and manage todo items to track tasks and plan work
    WRITE_TODOS = "write_todos"

    # Read and retrieve existing todo items and their status
    READ_TODOS = "read_todos"

    # Read and access file contents from the file system
    READ_FILE = "read_file"

    # Search through available MCP (Model Context Protocol) tools and capabilities
    MCP_SEARCH = "mcp_search"

    # Query and retrieve information from the organization's knowledge base by source
    KNOWLEDGE_BASE = "knowledge_base"

    # Resolve user identity by name with fuzzy matching
    KNOWLEDGE_BASE_IDP = "identity_resolver"

    # Search the web for real-time information and current data
    WEB_SEARCH = "web_search"

    # Scrape content from specific URLs with optional highlights
    WEB_SCRAPE = "web_scrape"

    # Execute MCP (Model Context Protocol) tools and operations
    EXECUTE_MCP = "execute_mcp"

    # Execute workflow orchestrations and automations
    WORKFLOW_EXECUTION = "workflow_execution"

    # Store important information and context into long-term memory
    ADD_MEMORY = "add_memory"

    # Retrieve previously stored memories and context from long-term memory
    GET_MEMORY = "get_memory"
