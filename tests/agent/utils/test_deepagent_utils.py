"""Tests for app/agent/utils/deepagent_utils.py"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch


class TestCreateDeepAgent:
    """Tests for create_deep_agent function"""

    @pytest.fixture
    def default_params(self):
        """Default parameters for create_deep_agent"""
        return {
            "provider": "openai",
            "model_name": "gpt-4",
            "use_knowledge": False,
            "use_search": False,
            "user_id": "user-123",
            "organisation_id": "org-456",
            "conversation_id": "conv-789",
            "agent_id": None,
            "use_memory": True,
            "mcp_ids": None,
            "use_thinking": False,
            "is_global": True,
            "timezone": "UTC",
            "kb_source": None,
            "user_name": None,
            "user_email": None,
        }

    @pytest.mark.asyncio
    async def test_create_deep_agent_raises_when_non_global_without_agent_id(
        self, default_params
    ):
        """Test that ValueError is raised when is_global=False and agent_id is not provided"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "is_global": False, "agent_id": None}

        with pytest.raises(ValueError) as exc_info:
            await create_deep_agent(**params)
        assert "agent_id is required for non-global agents" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_create_deep_agent_global_agent_success(self, default_params):
        """Test successful creation of global agent"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                    return_value=mock_agent
                ) as mock_create:
                    result = await create_deep_agent(**default_params)

                    assert result == mock_agent
                    mock_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_deep_agent_with_knowledge_subagent(self, default_params):
        """Test that knowledge base subagent is created when use_knowledge=True"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "use_knowledge": True}

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_kb_subagent = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.create_knowledge_base_subagent",
                    return_value=mock_kb_subagent
                ) as mock_kb_create:
                    with patch(
                        "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                        return_value=mock_agent
                    ) as mock_create:
                        result = await create_deep_agent(**params)

                        mock_kb_create.assert_called_once()
                        # Verify subagents includes the knowledge base subagent
                        call_kwargs = mock_create.call_args.kwargs
                        assert mock_kb_subagent in call_kwargs["subagents"]

    @pytest.mark.asyncio
    async def test_create_deep_agent_with_search_subagent(self, default_params):
        """Test that web search subagent is created when use_search=True"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "use_search": True}

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_search_subagent = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.create_web_search_subagent",
                    return_value=mock_search_subagent
                ) as mock_search_create:
                    with patch(
                        "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                        return_value=mock_agent
                    ) as mock_create:
                        result = await create_deep_agent(**params)

                        mock_search_create.assert_called_once()
                        call_kwargs = mock_create.call_args.kwargs
                        assert mock_search_subagent in call_kwargs["subagents"]

    @pytest.mark.asyncio
    async def test_create_deep_agent_with_memory_tools(self, default_params):
        """Test that memory tools are added when use_memory=True"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "use_memory": True}

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_store_tool = MagicMock()
        mock_retrieve_tool = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.memory_tools.get_store_memory_tool",
                    return_value=mock_store_tool
                ):
                    with patch(
                        "app.agent.utils.deepagent_utils.memory_tools.get_retrieve_memories_tool",
                        return_value=mock_retrieve_tool
                    ):
                        with patch(
                            "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                            return_value=mock_agent
                        ) as mock_create:
                            result = await create_deep_agent(**params)

                            call_kwargs = mock_create.call_args.kwargs
                            assert mock_store_tool in call_kwargs["tools"]
                            assert mock_retrieve_tool in call_kwargs["tools"]

    @pytest.mark.asyncio
    async def test_create_deep_agent_without_memory_tools(self, default_params):
        """Test that memory tools are NOT added when use_memory=False"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "use_memory": False}

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.memory_tools.get_store_memory_tool"
                ) as mock_store:
                    with patch(
                        "app.agent.utils.deepagent_utils.memory_tools.get_retrieve_memories_tool"
                    ) as mock_retrieve:
                        with patch(
                            "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                            return_value=mock_agent
                        ):
                            result = await create_deep_agent(**params)

                            # Memory tools should NOT be called when use_memory=False
                            mock_store.assert_not_called()
                            mock_retrieve.assert_not_called()

    @pytest.mark.asyncio
    async def test_create_deep_agent_non_global_fetches_agent_config(self, default_params):
        """Test that non-global agent fetches agent config"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "is_global": False, "agent_id": "agent-123"}

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_agent_fetch = MagicMock()
        mock_agent_fetch.fetch_agent_config = AsyncMock(return_value={
            "name": "Test Agent",
            "description": "A test agent",
            "system_message": "You are a test agent",
            "tone": "friendly",
            "agent_topic_type": "general",
            "mcp_server_ids": [],
            "mcps": [],
        })

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.get_agent_fetch_service",
                    return_value=mock_agent_fetch
                ):
                    with patch(
                        "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                        return_value=mock_agent
                    ):
                        result = await create_deep_agent(**params)

                        mock_agent_fetch.fetch_agent_config.assert_called_once_with("agent-123")

    @pytest.mark.asyncio
    async def test_create_deep_agent_with_existing_summary(self, default_params):
        """Test that existing conversation summary is added to instructions"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)

        # Mock existing state with summary
        mock_state = MagicMock()
        mock_state.values = {"summary": "Previous conversation context"}

        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=mock_state)
        mock_agent = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                    return_value=mock_agent
                ) as mock_create:
                    result = await create_deep_agent(**default_params)

                    call_kwargs = mock_create.call_args.kwargs
                    # Verify summary is included in system_prompt
                    assert "CONVERSATION SUMMARY" in call_kwargs["system_prompt"]
                    assert "Previous conversation context" in call_kwargs["system_prompt"]

    @pytest.mark.asyncio
    async def test_create_deep_agent_with_mcp_ids(self, default_params):
        """Test that MCP subagent is created when mcp_ids are provided"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {**default_params, "mcp_ids": ["mcp-1", "mcp-2"]}

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_mcp_subagent = MagicMock()
        mock_agent_fetch = MagicMock()
        mock_agent_fetch.fetch_mcps_by_ids = AsyncMock(return_value=[
            {"id": "mcp-1", "name": "MCP 1", "tools": []},
            {"id": "mcp-2", "name": "MCP 2", "tools": []},
        ])

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.get_agent_fetch_service",
                    return_value=mock_agent_fetch
                ):
                    with patch(
                        "app.agent.utils.deepagent_utils.create_mcp_subagent",
                        return_value=mock_mcp_subagent
                    ) as mock_mcp_create:
                        with patch(
                            "app.agent.utils.deepagent_utils.build_mcp_supervisor_prompt",
                            return_value="MCP tools info"
                        ):
                            with patch(
                                "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                                return_value=mock_agent
                            ) as mock_create:
                                result = await create_deep_agent(**params)

                                mock_mcp_create.assert_called_once()
                                call_kwargs = mock_create.call_args.kwargs
                                assert mock_mcp_subagent in call_kwargs["subagents"]

    @pytest.mark.asyncio
    async def test_create_deep_agent_non_global_with_mcp_from_agent_config(
        self, default_params
    ):
        """Test non-global agent uses MCPs from agent config"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {
            **default_params,
            "is_global": False,
            "agent_id": "agent-123",
            "mcp_ids": ["mcp-1"],
        }

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_mcp_subagent = MagicMock()
        mock_agent_fetch = MagicMock()
        mock_agent_fetch.fetch_agent_config = AsyncMock(return_value={
            "name": "Test Agent",
            "description": "A test agent",
            "system_message": "You are a test agent",
            "tone": "friendly",
            "agent_topic_type": "general",
            "mcp_server_ids": ["mcp-1"],
            "mcps": [{"id": "mcp-1", "name": "MCP 1", "tools": []}],
        })

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.get_agent_fetch_service",
                    return_value=mock_agent_fetch
                ):
                    with patch(
                        "app.agent.utils.deepagent_utils.create_mcp_subagent",
                        return_value=mock_mcp_subagent
                    ) as mock_mcp_create:
                        with patch(
                            "app.agent.utils.deepagent_utils.build_mcp_supervisor_prompt",
                            return_value="MCP tools info"
                        ):
                            with patch(
                                "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                                return_value=mock_agent
                            ):
                                result = await create_deep_agent(**params)

                                # MCP subagent should be created using MCPs from agent config
                                mock_mcp_create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_deep_agent_state_loading_exception(self, default_params):
        """Test that exception during state loading is handled gracefully"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)

        # Mock checkpointer that raises exception on get
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(side_effect=Exception("State loading failed"))
        mock_agent = MagicMock()

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                    return_value=mock_agent
                ):
                    # Should not raise, exception is caught
                    result = await create_deep_agent(**default_params)
                    assert result == mock_agent

    @pytest.mark.asyncio
    async def test_create_deep_agent_non_global_with_mcp_ids_from_agent_config(
        self, default_params
    ):
        """Test non-global agent extracts mcp_ids from agent config when not provided"""
        from app.agent.utils.deepagent_utils import create_deep_agent

        params = {
            **default_params,
            "is_global": False,
            "agent_id": "agent-123",
            "mcp_ids": None,  # Not provided
        }

        mock_model = MagicMock()
        mock_model.bind = MagicMock(return_value=mock_model)
        mock_checkpointer = MagicMock()
        mock_checkpointer.get = MagicMock(return_value=None)
        mock_agent = MagicMock()
        mock_mcp_subagent = MagicMock()
        mock_agent_fetch = MagicMock()
        mock_agent_fetch.fetch_agent_config = AsyncMock(return_value={
            "name": "Test Agent",
            "description": "A test agent",
            "system_message": "You are a test agent",
            "tone": "friendly",
            "agent_topic_type": "general",
            "mcp_server_ids": ["mcp-from-config"],  # MCPs from agent config
            "mcps": [{"id": "mcp-from-config", "name": "MCP From Config", "tools": []}],
        })

        with patch(
            "app.agent.utils.deepagent_utils.get_chat_model",
            return_value=mock_model
        ):
            with patch(
                "app.agent.utils.deepagent_utils.get_mongodb_saver",
                return_value=mock_checkpointer
            ):
                with patch(
                    "app.agent.utils.deepagent_utils.get_agent_fetch_service",
                    return_value=mock_agent_fetch
                ):
                    with patch(
                        "app.agent.utils.deepagent_utils.create_mcp_subagent",
                        return_value=mock_mcp_subagent
                    ) as mock_mcp_create:
                        with patch(
                            "app.agent.utils.deepagent_utils.build_mcp_supervisor_prompt",
                            return_value="MCP tools info"
                        ):
                            with patch(
                                "app.agent.utils.deepagent_utils.create_ruh_deep_agent",
                                return_value=mock_agent
                            ):
                                result = await create_deep_agent(**params)

                                # MCP subagent should be created using MCPs from agent config
                                mock_mcp_create.assert_called_once()
