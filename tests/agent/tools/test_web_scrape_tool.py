"""
Tests for app/agent/tools/web_scrape_tool.py
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.agent.tools.web_scrape_tool import (
    _format_scrape_response,
    get_web_scrape_tool,
    _create_error_response,
)


class TestFormatScrapeResponse:
    """Test _format_scrape_response function"""

    def test_format_with_results(self):
        """Test formatting response with results and top-level context"""
        data = {
            "results": [
                {
                    "id": "result1",
                    "title": "Test Page 1",
                    "url": "https://example.com/1",
                    "publishedDate": "2024-01-01",
                    "summary": "This is a summary"
                },
                {
                    "id": "result2",
                    "title": "Test Page 2",
                    "url": "https://example.com/2",
                    "publishedDate": "2024-01-02",
                    "summary": "Another summary"
                }
            ],
            "context": "Combined context content from all pages for LLM RAG"
        }
        urls = ["https://example.com/1", "https://example.com/2"]

        result = _format_scrape_response(data, urls)
        parsed = json.loads(result)

        assert parsed["urls"] == urls
        assert len(parsed["results"]) == 2
        assert parsed["results"][0]["id"] == "result1"
        assert parsed["results"][0]["title"] == "Test Page 1"
        assert parsed["results"][0]["summary"] == "This is a summary"
        assert parsed["results"][1]["id"] == "result2"
        assert parsed["results"][1]["summary"] == "Another summary"
        # Context is at top level, not per-result
        assert parsed["context"] == "Combined context content from all pages for LLM RAG"

    def test_format_without_results(self):
        """Test formatting response without results key"""
        data = {"error": "No content found"}
        urls = ["https://example.com"]
        
        result = _format_scrape_response(data, urls)
        parsed = json.loads(result)
        
        assert parsed["error"] == "No content found"
        assert "urls" not in parsed

    def test_format_with_missing_fields(self):
        """Test formatting with missing optional fields"""
        data = {
            "results": [
                {
                    "title": "Test Page"
                }
            ]
        }
        urls = ["https://example.com"]
        
        result = _format_scrape_response(data, urls)
        parsed = json.loads(result)
        
        assert parsed["results"][0]["id"] == ""
        assert parsed["results"][0]["title"] == "Test Page"
        assert parsed["results"][0]["url"] == ""
        assert parsed["results"][0]["publishedDate"] == ""

    def test_format_empty_results(self):
        """Test formatting with empty results list"""
        data = {"results": []}
        urls = ["https://example.com"]

        result = _format_scrape_response(data, urls)
        parsed = json.loads(result)

        assert parsed["urls"] == urls
        assert len(parsed["results"]) == 0

    def test_format_with_subpages(self):
        """Test formatting response with subpages"""
        data = {
            "results": [
                {
                    "id": "main",
                    "title": "Main Page",
                    "url": "https://example.com",
                    "publishedDate": "2024-01-01",
                    "summary": "Main page summary",
                    "subpages": [
                        {
                            "id": "sub1",
                            "title": "Pricing",
                            "url": "https://example.com/pricing",
                            "summary": "Pricing summary"
                        },
                        {
                            "id": "sub2",
                            "title": "FAQ",
                            "url": "https://example.com/faq",
                            "summary": "FAQ summary"
                        }
                    ]
                }
            ],
            "context": "Combined context from main page and all subpages"
        }
        urls = ["https://example.com"]

        result = _format_scrape_response(data, urls)
        parsed = json.loads(result)

        assert parsed["urls"] == urls
        assert len(parsed["results"]) == 1
        assert parsed["results"][0]["summary"] == "Main page summary"
        assert "subpages" in parsed["results"][0]
        assert len(parsed["results"][0]["subpages"]) == 2
        assert parsed["results"][0]["subpages"][0]["title"] == "Pricing"
        assert parsed["results"][0]["subpages"][0]["summary"] == "Pricing summary"
        assert parsed["results"][0]["subpages"][1]["title"] == "FAQ"
        assert parsed["context"] == "Combined context from main page and all subpages"


class TestCreateErrorResponse:
    """Test _create_error_response function"""

    def test_creates_error_command(self):
        """Test that error response creates proper Command"""
        result = _create_error_response("Test error message", "test-tool-id")
        
        assert result is not None
        message = result.update["messages"][0]
        content = json.loads(message.content)
        assert content["isError"] is True
        assert content["error"] == "Test error message"


class TestGetWebScrapeTool:
    """Test get_web_scrape_tool function"""

    def test_tool_creation(self):
        """Test that tool can be created with user context"""
        tool = get_web_scrape_tool(
            user_id="user123",
            conversation_id="conv123",
            agent_id="agent123",
            organisation_id="org123"
        )

        assert tool is not None
        assert tool.name == "web_scrape"
        assert "url" in tool.description.lower() or "scrape" in tool.description.lower()

    def test_tool_creation_minimal(self):
        """Test tool creation with minimal parameters"""
        tool = get_web_scrape_tool(user_id="user123")

        assert tool is not None
        assert tool.name == "web_scrape"

    @pytest.mark.asyncio
    async def test_web_scrape_empty_urls(self):
        """Test web scrape with empty urls returns error"""
        tool = get_web_scrape_tool(user_id="user123")

        with patch("app.agent.tools.web_scrape_tool.trace_operation") as mock_trace:
            mock_trace.return_value.__enter__ = MagicMock(return_value=MagicMock())
            mock_trace.return_value.__exit__ = MagicMock(return_value=False)

            result = await tool.coroutine(urls=[], tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "empty" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_scrape_too_many_urls(self):
        """Test web scrape with more than 10 URLs returns error"""
        tool = get_web_scrape_tool(user_id="user123")

        urls = [f"https://example.com/{i}" for i in range(11)]

        with patch("app.agent.tools.web_scrape_tool.trace_operation") as mock_trace:
            mock_trace.return_value.__enter__ = MagicMock(return_value=MagicMock())
            mock_trace.return_value.__exit__ = MagicMock(return_value=False)

            result = await tool.coroutine(urls=urls, tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "10" in content["error"]

    @pytest.mark.asyncio
    async def test_web_scrape_empty_url_in_list(self):
        """Test web scrape with empty string in URLs list"""
        tool = get_web_scrape_tool(user_id="user123")

        with patch("app.agent.tools.web_scrape_tool.trace_operation") as mock_trace:
            mock_trace.return_value.__enter__ = MagicMock(return_value=MagicMock())
            mock_trace.return_value.__exit__ = MagicMock(return_value=False)

            result = await tool.coroutine(urls=["https://example.com", ""], tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "non-empty" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_scrape_success(self):
        """Test successful web scrape"""
        tool = get_web_scrape_tool(user_id="user123", conversation_id="conv123")

        mock_response_data = {
            "results": [
                {
                    "id": "result1",
                    "title": "Test Page",
                    "url": "https://example.com",
                    "publishedDate": "2024-01-01",
                    "summary": "This is a summary of the page"
                }
            ],
            "context": "Combined context content for LLM RAG"
        }

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()
            with patch("app.agent.tools.web_scrape_tool.store_sources", new_callable=AsyncMock):
                result = await tool.coroutine(
                    urls=["https://example.com"],
                    tool_call_id="test-id",
                    config={"configurable": {"conversation_id": "conv123"}}
                )

                assert result is not None
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is False
                assert len(content["results"]) == 1
                assert content["results"][0]["summary"] == "This is a summary of the page"
                assert content["context"] == "Combined context content for LLM RAG"

    @pytest.mark.asyncio
    async def test_web_scrape_with_subpages(self):
        """Test web scrape with subpages parameter"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_response_data = {
            "results": [
                {
                    "id": "main",
                    "title": "Main Page",
                    "url": "https://example.com",
                    "summary": "Main page summary",
                    "subpages": [
                        {"id": "sub1", "title": "Pricing", "url": "https://example.com/pricing", "summary": "Pricing info"}
                    ]
                }
            ]
        }

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()
            with patch("app.agent.tools.web_scrape_tool.store_sources", new_callable=AsyncMock):
                result = await tool.coroutine(
                    urls=["https://example.com"],
                    subpages=5,
                    subpage_target=["pricing", "faq"],
                    tool_call_id="test-id"
                )

                assert result is not None
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is False
                assert len(content["results"][0]["subpages"]) == 1
                assert content["results"][0]["subpages"][0]["title"] == "Pricing"

    @pytest.mark.asyncio
    async def test_web_scrape_multiple_urls(self):
        """Test web scrape with multiple URLs"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_response_data = {
            "results": [
                {"id": "r1", "title": "Page 1", "url": "https://example.com/1", "summary": "Summary 1"},
                {"id": "r2", "title": "Page 2", "url": "https://example.com/2", "summary": "Summary 2"}
            ]
        }

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()
            with patch("app.agent.tools.web_scrape_tool.store_sources", new_callable=AsyncMock):
                result = await tool.coroutine(
                    urls=["https://example.com/1", "https://example.com/2"],
                    tool_call_id="test-id"
                )

                assert result is not None
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is False
                assert len(content["results"]) == 2

    @pytest.mark.asyncio
    async def test_web_scrape_api_error(self):
        """Test web scrape with API error"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text = AsyncMock(return_value="Internal Server Error")

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()

            result = await tool.coroutine(
                urls=["https://example.com"],
                tool_call_id="test-id"
            )

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "500" in content["error"]

    @pytest.mark.asyncio
    async def test_web_scrape_json_parse_error(self):
        """Test web scrape with JSON parse error"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(side_effect=Exception("JSON parse error"))

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()

            result = await tool.coroutine(
                urls=["https://example.com"],
                tool_call_id="test-id"
            )

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "parse" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_scrape_connection_error(self):
        """Test web scrape with connection error"""
        import aiohttp
        tool = get_web_scrape_tool(user_id="user123")

        mock_client_session = AsyncMock()
        mock_client_session.__aenter__ = AsyncMock(side_effect=aiohttp.ClientError("Connection failed"))
        mock_client_session.__aexit__ = AsyncMock()

        with patch("aiohttp.ClientSession", return_value=mock_client_session):
            result = await tool.coroutine(
                urls=["https://example.com"],
                tool_call_id="test-id"
            )

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "connection" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_scrape_value_error(self):
        """Test web scrape with value error"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_client_session = AsyncMock()
        mock_client_session.__aenter__ = AsyncMock(side_effect=ValueError("Invalid value"))
        mock_client_session.__aexit__ = AsyncMock()

        with patch("aiohttp.ClientSession", return_value=mock_client_session):
            result = await tool.coroutine(
                urls=["https://example.com"],
                tool_call_id="test-id"
            )

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True

    @pytest.mark.asyncio
    async def test_web_scrape_unexpected_error(self):
        """Test web scrape with unexpected error"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_client_session = AsyncMock()
        mock_client_session.__aenter__ = AsyncMock(side_effect=RuntimeError("Unexpected error"))
        mock_client_session.__aexit__ = AsyncMock()

        with patch("aiohttp.ClientSession", return_value=mock_client_session):
            result = await tool.coroutine(
                urls=["https://example.com"],
                tool_call_id="test-id"
            )

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "unexpected" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_scrape_with_subpage_target_only(self):
        """Test web scrape with subpage_target but no subpages count"""
        tool = get_web_scrape_tool(user_id="user123")

        mock_response_data = {
            "results": [{"id": "r1", "title": "Page", "url": "https://example.com", "summary": "Summary"}]
        }

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()
            with patch("app.agent.tools.web_scrape_tool.store_sources", new_callable=AsyncMock):
                result = await tool.coroutine(
                    urls=["https://example.com"],
                    subpage_target=["docs", "pricing"],
                    tool_call_id="test-id"
                )

                assert result is not None
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is False

