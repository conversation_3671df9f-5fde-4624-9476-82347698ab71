"""Tests for global agent prompt generation."""

import pytest
from app.agent.system_prompts.global_agent_prompt import get_global_agent_prompt


class TestGetGlobalAgentPrompt:
    """Tests for get_global_agent_prompt function."""

    def test_basic_prompt_generation(self):
        """Test generating basic global prompt."""
        result = get_global_agent_prompt()

        # Check for Ruh branding (updated from "Ruh Global Agent" to "Ruh Assistant")
        assert "Ruh" in result
        assert "intro-and-description" in result

    def test_prompt_with_all_features_enabled(self):
        """Test global prompt with all features enabled."""
        result = get_global_agent_prompt(
            use_knowledge=True,
            use_search=True,
            use_memory=True,
            has_mcp_tools=True,
            current_datetime="2025-12-08 14:30 (UTC)",
            user_id="user-123",
            conversation_id="conv-456",
            agent_id="agent-789",
            organisation_id="org-abc",
            timezone="UTC",
            user_name="<PERSON>",
            user_email="<EMAIL>",
        )
        
        # Memory should be enabled
        assert "memory-disabled-notice" not in result
        # Search should be enabled
        assert "web-search-disabled-notice" not in result
        # Knowledge should be enabled
        assert "knowledge-base-disabled-notice" not in result
        # Should not have disabled features section for MCP
        assert "Specialized Integrations" not in result or "disabled" not in result

    def test_prompt_with_all_features_disabled(self):
        """Test global prompt with all features disabled."""
        result = get_global_agent_prompt(
            use_knowledge=False,
            use_search=False,
            use_memory=False,
            has_mcp_tools=False,
        )
        
        assert "memory-disabled-notice" in result
        assert "web-search-disabled-notice" in result
        assert "knowledge-base-disabled-notice" in result
        assert "disabled-features-information" in result

    def test_prompt_contains_ruh_branding(self):
        """Test that Ruh branding is present."""
        result = get_global_agent_prompt()
        
        assert "Ruh" in result
        assert "ruh.ai" in result

    def test_prompt_contains_todo_instructions(self):
        """Test that TODO usage instructions are included."""
        result = get_global_agent_prompt()
        
        assert "todo" in result.lower() or "TODO" in result

    def test_prompt_contains_mcp_instructions(self):
        """Test that MCP usage instructions are included."""
        result = get_global_agent_prompt()
        
        assert "MCP" in result

    def test_prompt_contains_mcp_search_instructions(self):
        """Test that MCP search usage instructions are included."""
        result = get_global_agent_prompt()
        
        # MCP search instructions should be included for global agent
        assert len(result) > 1000  # Should be substantial

    def test_prompt_user_context_formatting(self):
        """Test that user context is properly formatted."""
        result = get_global_agent_prompt(
            user_id="test-user",
            conversation_id="test-conv",
            agent_id="test-agent",
            organisation_id="test-org",
            timezone="America/New_York",
            user_name="Alice",
            user_email="<EMAIL>",
        )
        
        # User context should be included in general instructions
        assert "Alice" in result or "test-user" in result

    def test_prompt_tool_result_handling(self):
        """Test that delegation instructions are included."""
        result = get_global_agent_prompt()

        # Check for delegation/subagent handling instructions (updated tags)
        assert "main-agent-delegation-instructions" in result
        assert "delegation" in result.lower()

    def test_prompt_subagent_instructions(self):
        """Test that subagent usage instructions are included."""
        result = get_global_agent_prompt()
        
        # Should have subagent-related content
        assert "subagent" in result.lower()

    def test_prompt_operation_guidelines(self):
        """Test that operation guidelines are included."""
        result = get_global_agent_prompt()
        
        # Operation guidelines should be present
        assert len(result) > 2000  # Substantial prompt with guidelines

    def test_prompt_memory_enabled(self):
        """Test prompt with memory enabled."""
        result = get_global_agent_prompt(use_memory=True)
        
        assert "memory-disabled-notice" not in result

    def test_prompt_memory_disabled(self):
        """Test prompt with memory disabled."""
        result = get_global_agent_prompt(use_memory=False)
        
        assert "memory-disabled-notice" in result
        assert "Long-term memory" in result

    def test_prompt_knowledge_with_email(self):
        """Test prompt with knowledge enabled and user email."""
        result = get_global_agent_prompt(
            use_knowledge=True,
            user_email="<EMAIL>",
        )
        
        assert "knowledge-base-disabled-notice" not in result

