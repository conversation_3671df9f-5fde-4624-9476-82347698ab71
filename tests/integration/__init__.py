"""
Integration Tests for Agent Platform

Integration tests verify that multiple components work together correctly,
including service-to-service communication, database operations, message queues,
and external API interactions.

Test Categories:
- cache/: Redis cache integration tests
- database/: MongoDB integration tests
- messaging/: Redis Streams integration tests
- workflows/: Cross-service workflow tests

Running Integration Tests:
    # Start test dependencies first
    docker-compose -f docker-compose.test.yml up -d
    
    # Run all integration tests
    poetry run pytest -m integration -v
    
    # Run specific category
    poetry run pytest tests/integration/cache/ -v -m integration
    
    # Cleanup
    docker-compose -f docker-compose.test.yml down -v
"""

