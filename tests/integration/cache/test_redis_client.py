"""
Integration tests for RedisClient.

These tests verify that the RedisClient works correctly with a real Redis instance.
Prerequisites: docker-compose -f docker-compose.test.yml up -d

Run with: poetry run pytest tests/integration/cache/test_redis_client.py -v -m integration
"""

import pytest
import asyncio

pytestmark = [pytest.mark.integration, pytest.mark.asyncio]


class TestRedisClientBasicOperations:
    """Test basic Redis operations with real Redis."""

    async def test_set_and_get_value(self, app_redis_client):
        """Test setting and getting a simple value."""
        key = "test:basic:value"
        value = "hello world"
        
        await app_redis_client.set_value(key, value)
        result = await app_redis_client.get_value(key)
        
        assert result == value

    async def test_set_and_get_dict_value(self, app_redis_client):
        """Test setting and getting a dictionary value (JSON serialization)."""
        key = "test:dict:value"
        value = {"name": "test", "count": 42, "nested": {"key": "value"}}
        
        await app_redis_client.set_value(key, value)
        result = await app_redis_client.get_value(key)
        
        assert result == value

    async def test_set_and_get_list_value(self, app_redis_client):
        """Test setting and getting a list value."""
        key = "test:list:value"
        value = [1, 2, 3, "four", {"five": 5}]
        
        await app_redis_client.set_value(key, value)
        result = await app_redis_client.get_value(key)
        
        assert result == value

    async def test_get_nonexistent_key(self, app_redis_client):
        """Test getting a key that doesn't exist."""
        result = await app_redis_client.get_value("nonexistent:key")
        assert result is None

    async def test_value_with_ttl(self, app_redis_client):
        """Test setting a value with TTL."""
        key = "test:ttl:value"
        value = "expires soon"
        
        await app_redis_client.set_value(key, value, ttl=1)
        
        # Value should exist immediately
        result = await app_redis_client.get_value(key)
        assert result == value
        
        # Wait for TTL to expire
        await asyncio.sleep(1.5)
        
        result = await app_redis_client.get_value(key)
        assert result is None

    async def test_delete_key(self, app_redis_client):
        """Test deleting a key."""
        key = "test:delete:key"
        await app_redis_client.set_value(key, "to be deleted")
        
        # Verify it exists
        assert await app_redis_client.exists(key) is True
        
        # Delete it
        await app_redis_client.delete(key)
        
        # Verify it's gone
        assert await app_redis_client.exists(key) is False

    async def test_exists(self, app_redis_client):
        """Test checking if a key exists."""
        key = "test:exists:key"
        
        # Should not exist initially
        assert await app_redis_client.exists(key) is False
        
        # Create it
        await app_redis_client.set_value(key, "exists")
        
        # Should exist now
        assert await app_redis_client.exists(key) is True

    async def test_increment(self, app_redis_client):
        """Test incrementing a counter."""
        key = "test:counter"
        
        # First increment creates the key
        result = await app_redis_client.increment(key)
        assert result == 1
        
        # Subsequent increments
        result = await app_redis_client.increment(key)
        assert result == 2
        
        result = await app_redis_client.increment(key, 5)
        assert result == 7


class TestRedisClientHashOperations:
    """Test Redis hash operations."""

    async def test_set_and_get_hash(self, app_redis_client):
        """Test setting and getting a hash."""
        key = "test:hash"
        value = {
            "field1": "value1",
            "field2": 42,
            "field3": {"nested": "dict"},
            "field4": [1, 2, 3],
        }
        
        await app_redis_client.set_hash(key, value)
        result = await app_redis_client.get_hash(key)
        
        assert result["field1"] == "value1"
        assert result["field2"] == 42
        assert result["field3"] == {"nested": "dict"}
        assert result["field4"] == [1, 2, 3]

    async def test_hash_with_ttl(self, app_redis_client):
        """Test hash with TTL."""
        key = "test:hash:ttl"
        value = {"field": "value"}
        
        await app_redis_client.set_hash(key, value, ttl=1)
        
        # Should exist immediately
        result = await app_redis_client.get_hash(key)
        assert result["field"] == "value"
        
        # Wait for TTL
        await asyncio.sleep(1.5)
        
        result = await app_redis_client.get_hash(key)
        assert result == {}

    async def test_hset_single_field(self, app_redis_client):
        """Test setting a single hash field."""
        key = "test:hash:single"
        
        await app_redis_client.hset(key, "field1", "value1")
        await app_redis_client.hset(key, "field2", {"nested": "value"})
        
        result = await app_redis_client.get_hash(key)
        assert result["field1"] == "value1"
        assert result["field2"] == {"nested": "value"}

    async def test_hincr(self, app_redis_client):
        """Test incrementing a hash field."""
        key = "test:hash:incr"

        result = await app_redis_client.hincr(key, "counter")
        assert result == 1

        result = await app_redis_client.hincr(key, "counter", 5)
        assert result == 6


class TestRedisClientListOperations:
    """Test Redis list operations."""

    async def test_set_and_get_list(self, app_redis_client):
        """Test setting and getting a list."""
        key = "test:list"
        values = ["item1", "item2", {"nested": "dict"}, 42]

        await app_redis_client.set_list(key, values)
        result = await app_redis_client.get_list(key)

        assert result == values

    async def test_set_empty_list(self, app_redis_client):
        """Test setting an empty list."""
        key = "test:list:empty"

        await app_redis_client.set_list(key, [])
        result = await app_redis_client.get_list(key)

        assert result == []

    async def test_list_with_ttl(self, app_redis_client):
        """Test list with TTL."""
        key = "test:list:ttl"
        values = ["item1", "item2"]

        await app_redis_client.set_list(key, values, ttl=1)

        # Should exist immediately
        result = await app_redis_client.get_list(key)
        assert result == values

        # Wait for TTL
        await asyncio.sleep(1.5)

        result = await app_redis_client.get_list(key)
        assert result == []

    async def test_rpush(self, app_redis_client):
        """Test pushing to a list."""
        key = "test:list:rpush"

        await app_redis_client.rpush(key, "item1")
        await app_redis_client.rpush(key, "item2", "item3")

        result = await app_redis_client.lrange(key, 0, -1)
        assert result == ["item1", "item2", "item3"]

    async def test_lrange(self, app_redis_client):
        """Test getting a range from a list."""
        key = "test:list:range"
        values = ["a", "b", "c", "d", "e"]

        await app_redis_client.set_list(key, values)

        # Get first 3
        result = await app_redis_client.lrange(key, 0, 2)
        assert result == ["a", "b", "c"]

        # Get last 2
        result = await app_redis_client.lrange(key, -2, -1)
        assert result == ["d", "e"]


class TestRedisClientScanOperations:
    """Test Redis scan operations."""

    async def test_scan_pattern(self, app_redis_client):
        """Test scanning for keys matching a pattern."""
        # Create some keys
        await app_redis_client.set_value("scan:test:1", "value1")
        await app_redis_client.set_value("scan:test:2", "value2")
        await app_redis_client.set_value("scan:other:1", "value3")
        await app_redis_client.set_value("different:key", "value4")

        # Scan for scan:test:* pattern
        result = await app_redis_client.scan("scan:test:*")

        assert len(result) == 2
        assert "scan:test:1" in result
        assert "scan:test:2" in result

    async def test_scan_all_keys(self, app_redis_client):
        """Test scanning all keys."""
        # Create some keys
        await app_redis_client.set_value("key1", "value1")
        await app_redis_client.set_value("key2", "value2")

        result = await app_redis_client.scan("*")

        assert len(result) >= 2
        assert "key1" in result
        assert "key2" in result


class TestRedisClientPubSub:
    """Test Redis pub/sub operations."""

    async def test_publish_and_subscribe(self, app_redis_client):
        """Test publishing and subscribing to a channel."""
        channel = "test:channel"
        message = "Hello, World!"

        # Subscribe to channel
        pubsub = await app_redis_client.subscribe(channel)

        # Publish message
        await app_redis_client.publish(channel, message)

        # Read message (first message is subscription confirmation)
        await pubsub.get_message(timeout=1)  # Skip subscription message
        received = await pubsub.get_message(timeout=1)

        assert received is not None
        assert received["type"] == "message"
        assert received["data"] == message

        # Cleanup
        await pubsub.unsubscribe(channel)
        await pubsub.close()


class TestRedisClientExpiration:
    """Test Redis expiration operations."""

    async def test_expire(self, app_redis_client):
        """Test setting expiration on an existing key."""
        key = "test:expire"
        await app_redis_client.set_value(key, "value")

        # Set expiration
        result = await app_redis_client.expire(key, 1)
        assert result is True

        # Key should exist
        assert await app_redis_client.exists(key) is True

        # Wait for expiration
        await asyncio.sleep(1.5)

        # Key should be gone
        assert await app_redis_client.exists(key) is False

