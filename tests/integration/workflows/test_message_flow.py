"""
Integration tests for message flow through Redis Streams.

These tests verify the end-to-end message flow from producer to consumer
using real Redis Streams infrastructure.

Prerequisites: docker-compose -f docker-compose.test.yml up -d

Run with: poetry run pytest tests/integration/workflows/test_message_flow.py -v -m integration
"""

import pytest
import asyncio
import uuid
from unittest.mock import patch

pytestmark = [pytest.mark.integration, pytest.mark.asyncio]


class TestProducerConsumerFlow:
    """Test producer-consumer message flow."""

    async def test_send_and_receive_message(self, app_redis_streams_manager, generate_test_message):
        """Test sending a message and receiving it via consumer."""
        stream = "test:flow:basic"
        group = "test-group"
        consumer_name = "test-consumer"
        
        # Create consumer
        consumer = app_redis_streams_manager.get_consumer(group, consumer_name)
        await consumer.ensure_consumer_group(stream)
        
        # Send message
        message_data = generate_test_message(message="Hello, World!")
        msg_id = await app_redis_streams_manager.producer.send_message(stream, message_data)
        
        assert msg_id is not None
        
        # Read message
        result = await app_redis_streams_manager.redis_client.xreadgroup(
            group, consumer_name, {stream: ">"}, count=1
        )
        
        assert stream in result
        assert len(result[stream]) == 1
        
        received_id, received_fields = result[stream][0]
        assert received_fields["message"] == "Hello, World!"

    async def test_message_acknowledgment(self, app_redis_streams_manager, generate_test_message):
        """Test that acknowledged messages are not redelivered."""
        stream = "test:flow:ack"
        group = "test-group"
        consumer_name = "test-consumer"
        
        # Setup
        consumer = app_redis_streams_manager.get_consumer(group, consumer_name)
        await consumer.ensure_consumer_group(stream)
        
        # Send message
        message_data = generate_test_message()
        msg_id = await app_redis_streams_manager.producer.send_message(stream, message_data)
        
        # Read message
        result = await app_redis_streams_manager.redis_client.xreadgroup(
            group, consumer_name, {stream: ">"}, count=1
        )
        
        # Acknowledge
        ack_result = await consumer.acknowledge_message(stream, msg_id)
        assert ack_result is True
        
        # Try to read again - should get nothing
        result = await app_redis_streams_manager.redis_client.xreadgroup(
            group, consumer_name, {stream: ">"}, count=1, block=100
        )
        
        assert stream not in result or len(result.get(stream, [])) == 0

    async def test_multiple_consumers_same_group(self, app_redis_streams_manager, generate_test_message):
        """Test that messages are distributed among consumers in the same group."""
        stream = "test:flow:multi-consumer"
        group = "test-group"
        
        # Create two consumers
        consumer1 = app_redis_streams_manager.get_consumer(group, "consumer-1")
        consumer2 = app_redis_streams_manager.get_consumer(group, "consumer-2")
        
        await consumer1.ensure_consumer_group(stream)
        
        # Send multiple messages
        for i in range(10):
            await app_redis_streams_manager.producer.send_message(
                stream, generate_test_message(message=f"Message {i}")
            )
        
        # Both consumers read
        result1 = await app_redis_streams_manager.redis_client.xreadgroup(
            group, "consumer-1", {stream: ">"}, count=10
        )
        result2 = await app_redis_streams_manager.redis_client.xreadgroup(
            group, "consumer-2", {stream: ">"}, count=10
        )
        
        # Messages should be distributed (not all to one consumer)
        count1 = len(result1.get(stream, []))
        count2 = len(result2.get(stream, []))
        
        # Total should be 10
        assert count1 + count2 == 10

    async def test_pending_messages_after_crash(self, app_redis_streams_manager, generate_test_message):
        """Test that unacknowledged messages remain pending."""
        stream = "test:flow:pending"
        group = "test-group"
        consumer_name = "test-consumer"
        
        # Setup
        consumer = app_redis_streams_manager.get_consumer(group, consumer_name)
        await consumer.ensure_consumer_group(stream)
        
        # Send message
        message_data = generate_test_message()
        msg_id = await app_redis_streams_manager.producer.send_message(stream, message_data)
        
        # Read but don't acknowledge (simulating crash)
        await app_redis_streams_manager.redis_client.xreadgroup(
            group, consumer_name, {stream: ">"}, count=1
        )
        
        # Check pending messages
        pending = await app_redis_streams_manager.redis_client.xpending_range(
            stream, group, count=10
        )
        
        assert len(pending) == 1
        assert pending[0]["message_id"] == msg_id


class TestResponseStreamFlow:
    """Test response stream patterns."""

    async def test_response_stream_creation(self, app_redis_streams_manager):
        """Test creating and reading from a response stream."""
        conversation_id = f"conv-{uuid.uuid4().hex[:8]}"
        request_id = f"req-{uuid.uuid4().hex[:8]}"
        
        # Send response
        response_data = {
            "type": "text",
            "content": "Hello from agent",
            "done": False
        }
        
        msg_id = await app_redis_streams_manager.producer.send_agent_response(
            conversation_id=conversation_id,
            response_data=response_data,
            request_id=request_id
        )
        
        assert msg_id is not None
        
        # Read response using response reader
        reader = app_redis_streams_manager.get_response_reader()
        
        # The stream name format
        env = app_redis_streams_manager.settings.environment
        stream = f"{env}:agent:chat:responses:{conversation_id}-{request_id}"
        
        result = await app_redis_streams_manager.redis_client.xread({stream: "0"})

        assert stream in result
        assert len(result[stream]) == 1


class TestDeadLetterHandling:
    """Test dead letter queue handling."""

    async def test_move_failed_message_to_dead_letter(self, app_redis_streams_manager, generate_test_message):
        """Test moving a failed message to dead letter queue."""
        from app.services.redis_streams import StreamMessage

        original_stream = "test:flow:dead-letter"

        # Create a message that "failed"
        message = StreamMessage(
            id="test-failed-id",
            fields=generate_test_message(message="Failed message"),
            stream=original_stream
        )

        # Move to dead letter
        await app_redis_streams_manager.move_to_dead_letter(
            original_stream, message, "test_processing_error"
        )

        # Verify message is in dead letter stream
        dl_stream = f"{original_stream}:dead_letter"
        result = await app_redis_streams_manager.redis_client.xread({dl_stream: "0"})

        assert dl_stream in result
        # Find the moved message (not the init message)
        moved_messages = [
            msg for msg in result[dl_stream]
            if msg[1].get("error_reason") == "test_processing_error"
        ]
        assert len(moved_messages) == 1

        # Verify error metadata
        _, fields = moved_messages[0]
        assert fields["original_stream"] == original_stream
        assert fields["original_message_id"] == "test-failed-id"


class TestStreamMonitoring:
    """Test stream monitoring and health checks."""

    async def test_get_stream_info(self, app_redis_streams_manager, generate_test_message):
        """Test getting stream information."""
        stream = "test:flow:info"

        # Add some messages
        for i in range(5):
            await app_redis_streams_manager.producer.send_message(
                stream, generate_test_message(message=f"Info test {i}")
            )

        # Get info
        info = await app_redis_streams_manager.get_stream_info(stream)

        assert info["length"] == 5
        assert info["first_entry"] is not None
        assert info["last_entry"] is not None

    async def test_monitor_consumer_group_health(self, app_redis_streams_manager, generate_test_message):
        """Test monitoring consumer group health."""
        stream = "test:flow:health"
        group = "health-test-group"
        consumer_name = "health-consumer"

        # Setup
        consumer = app_redis_streams_manager.get_consumer(group, consumer_name)
        await consumer.ensure_consumer_group(stream)

        # Add and read some messages (but don't ack)
        for i in range(3):
            await app_redis_streams_manager.producer.send_message(
                stream, generate_test_message()
            )

        await app_redis_streams_manager.redis_client.xreadgroup(
            group, consumer_name, {stream: ">"}, count=10
        )

        # Check health
        health = await app_redis_streams_manager.monitor_consumer_group_health(stream, group)

        assert health["pending_messages"] == 3
        assert health["consumers"] >= 1


class TestConcurrentOperations:
    """Test concurrent stream operations."""

    async def test_concurrent_producers(self, app_redis_streams_manager, generate_test_message):
        """Test multiple producers sending concurrently."""
        stream = "test:flow:concurrent"
        num_messages = 50

        # Send messages concurrently
        tasks = [
            app_redis_streams_manager.producer.send_message(
                stream, generate_test_message(message=f"Concurrent {i}")
            )
            for i in range(num_messages)
        ]

        results = await asyncio.gather(*tasks)

        # All should succeed
        assert len(results) == num_messages
        assert all(r is not None for r in results)

        # Verify all messages are in stream
        length = await app_redis_streams_manager.redis_client.xlen(stream)
        assert length == num_messages

    async def test_concurrent_read_write(self, app_redis_streams_manager, generate_test_message):
        """Test concurrent reading and writing."""
        stream = "test:flow:read-write"
        group = "rw-group"

        # Setup consumer group
        consumer = app_redis_streams_manager.get_consumer(group, "rw-consumer")
        await consumer.ensure_consumer_group(stream)

        # Start writing
        async def write_messages():
            for i in range(20):
                await app_redis_streams_manager.producer.send_message(
                    stream, generate_test_message(message=f"RW {i}")
                )
                await asyncio.sleep(0.01)

        # Start reading
        async def read_messages():
            messages = []
            for _ in range(10):  # Try 10 times
                result = await app_redis_streams_manager.redis_client.xreadgroup(
                    group, "rw-consumer", {stream: ">"}, count=5, block=100
                )
                if stream in result:
                    messages.extend(result[stream])
            return messages

        # Run concurrently
        write_task = asyncio.create_task(write_messages())
        read_task = asyncio.create_task(read_messages())

        await write_task
        messages = await read_task

        # Should have read some messages
        assert len(messages) > 0

