"""
Integration Test Configuration and Fixtures

This module provides shared fixtures for integration tests that use Docker
containers for Redis, MongoDB, and Qdrant.

IMPORTANT: These tests ONLY run against Docker containers started via:
    docker-compose -f docker-compose.test.yml up -d

Tests will be SKIPPED if Docker containers are not running.
This prevents accidentally running tests against production services.

The test containers use non-standard ports to avoid conflicts:
    - Redis: 6380 (not 6379)
    - MongoDB: 27018 (not 27017)
    - Qdrant: 6334 (not 6333)
"""

import os
import asyncio
import pytest
from typing import AsyncGenerator, Generator
from unittest.mock import patch

import redis.asyncio as async_redis
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure

# Integration test environment configuration
# These are HARDCODED to Docker test container ports - not configurable via env vars
# to prevent accidentally connecting to production services
INTEGRATION_CONFIG = {
    "redis_host": "localhost",
    "redis_port": 6380,  # Docker test container port
    "redis_db": 0,
    "mongodb_url": "*************************************************",  # Docker test container
    "mongodb_db": "test_db",
    "qdrant_host": "localhost",
    "qdrant_port": 6334,  # Docker test container port
    "environment": "test",
}


def _check_redis_available() -> bool:
    """Check if Redis test container is available."""
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex((INTEGRATION_CONFIG["redis_host"], INTEGRATION_CONFIG["redis_port"]))
        sock.close()
        return result == 0
    except Exception:
        return False


def _check_mongodb_available() -> bool:
    """Check if MongoDB test container is available."""
    try:
        client = MongoClient(
            INTEGRATION_CONFIG["mongodb_url"],
            serverSelectionTimeoutMS=1000
        )
        client.admin.command('ping')
        client.close()
        return True
    except (ConnectionFailure, Exception):
        return False


# Check container availability at module load time
REDIS_AVAILABLE = _check_redis_available()
MONGODB_AVAILABLE = _check_mongodb_available()
DOCKER_AVAILABLE = REDIS_AVAILABLE and MONGODB_AVAILABLE

# Skip message for when Docker is not running
DOCKER_SKIP_REASON = (
    "Docker test containers not running. "
    "Start with: docker-compose -f docker-compose.test.yml up -d"
)


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test requiring Docker containers"
    )


def pytest_collection_modifyitems(config, items):
    """Skip integration tests if Docker containers are not available."""
    if DOCKER_AVAILABLE:
        return  # Docker is running, don't skip

    skip_marker = pytest.mark.skip(reason=DOCKER_SKIP_REASON)
    for item in items:
        if "integration" in item.keywords:
            item.add_marker(skip_marker)


@pytest.fixture(scope="session")
def event_loop():
    """Create an event loop for the entire test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def integration_config() -> dict:
    """Provide integration test configuration."""
    return INTEGRATION_CONFIG.copy()


# ============================================================================
# Redis Fixtures
# ============================================================================

@pytest.fixture(scope="session")
async def redis_connection(integration_config) -> AsyncGenerator[async_redis.Redis, None]:
    """
    Create a session-scoped Redis connection for integration tests.
    This connection is reused across all tests in the session.
    """
    client = async_redis.Redis(
        host=integration_config["redis_host"],
        port=integration_config["redis_port"],
        db=integration_config["redis_db"],
        decode_responses=True,
    )
    
    try:
        # Verify connection
        await client.ping()
        yield client
    finally:
        await client.close()


@pytest.fixture
async def redis_client(redis_connection) -> AsyncGenerator[async_redis.Redis, None]:
    """
    Function-scoped Redis client that cleans up after each test.
    Uses the session-scoped connection but flushes the database after each test.
    """
    yield redis_connection
    # Clean up after each test
    await redis_connection.flushdb()


@pytest.fixture
async def redis_client_binary(integration_config) -> AsyncGenerator[async_redis.Redis, None]:
    """
    Redis client without decode_responses for binary data tests.
    """
    client = async_redis.Redis(
        host=integration_config["redis_host"],
        port=integration_config["redis_port"],
        db=integration_config["redis_db"],
        decode_responses=False,
    )
    
    try:
        await client.ping()
        yield client
    finally:
        await client.flushdb()
        await client.close()


# ============================================================================
# MongoDB Fixtures
# ============================================================================

@pytest.fixture(scope="session")
def mongodb_client(integration_config) -> Generator[MongoClient, None, None]:
    """
    Create a session-scoped MongoDB client for integration tests.
    """
    client = MongoClient(integration_config["mongodb_url"])
    
    try:
        # Verify connection
        client.admin.command('ping')
        yield client
    finally:
        client.close()


@pytest.fixture
def mongodb_db(mongodb_client, integration_config):
    """
    Function-scoped MongoDB database that cleans up after each test.
    """
    db = mongodb_client[integration_config["mongodb_db"]]
    yield db
    # Clean up all collections after each test
    for collection_name in db.list_collection_names():
        db[collection_name].delete_many({})


@pytest.fixture
def mongodb_collection(mongodb_db):
    """
    Provide a test collection that is cleaned up after each test.
    """
    collection = mongodb_db["test_collection"]
    yield collection
    # Clean up: delete all documents and drop all non-_id indexes
    collection.delete_many({})
    # Drop all indexes except the default _id index
    collection.drop_indexes()


# ============================================================================
# App-Specific Fixtures (RedisClient, Settings Mock)
# ============================================================================

@pytest.fixture
def mock_integration_settings(integration_config):
    """
    Create a mock Settings object configured for integration tests.
    """
    from unittest.mock import Mock
    from dataclasses import dataclass

    @dataclass
    class MockRedisConfig:
        redis_host: str = integration_config["redis_host"]
        redis_port: int = integration_config["redis_port"]
        redis_db: int = integration_config["redis_db"]
        password: str = None
        consumer_batch_size: int = 10

    @dataclass
    class MockMongoDBConfig:
        url: str = integration_config["mongodb_url"]

    mock_settings = Mock()
    mock_settings.redis = MockRedisConfig()
    mock_settings.mongodb = MockMongoDBConfig()
    mock_settings.environment = integration_config["environment"]

    return mock_settings


@pytest.fixture
async def app_redis_client(mock_integration_settings):
    """
    Create an app RedisClient configured for integration tests.
    This patches get_settings to use integration test configuration.
    """
    with patch("app.services.redis_client.get_settings", return_value=mock_integration_settings):
        from app.services.redis_client import RedisClient

        client = RedisClient()
        await client.initialize()

        yield client

        # Cleanup
        await client.flushdb(async_flush=False)
        await client.close()


@pytest.fixture
async def app_redis_streams_manager(mock_integration_settings):
    """
    Create a RedisStreamsManager configured for integration tests.
    """
    with patch("app.services.redis_client.get_settings", return_value=mock_integration_settings):
        with patch("app.services.redis_streams.get_settings", return_value=mock_integration_settings):
            from app.services.redis_streams import RedisStreamsManager

            manager = RedisStreamsManager()
            await manager.initialize()

            yield manager

            # Cleanup - flush Redis
            await manager.redis_client.flushdb(async_flush=False)
            await manager.close()


# ============================================================================
# Test Data Generators
# ============================================================================

@pytest.fixture
def generate_test_message():
    """Factory fixture to generate test messages for Redis Streams."""
    import time
    import uuid

    def _generate(
        conversation_id: str = None,
        message: str = "Test message",
        **extra_fields
    ):
        return {
            "conversation_id": conversation_id or str(uuid.uuid4()),
            "message": message,
            "timestamp": time.time(),
            "request_id": str(uuid.uuid4()),
            **extra_fields
        }

    return _generate


@pytest.fixture
def generate_agent_context():
    """Factory fixture to generate test agent context."""
    import uuid

    def _generate(
        user_id: str = None,
        agent_id: str = None,
        organisation_id: str = None,
    ):
        return {
            "user_id": user_id or f"test-user-{uuid.uuid4().hex[:8]}",
            "agent_id": agent_id or f"test-agent-{uuid.uuid4().hex[:8]}",
            "organisation_id": organisation_id or f"test-org-{uuid.uuid4().hex[:8]}",
            "session_id": f"test-session-{uuid.uuid4().hex[:8]}",
        }

    return _generate


# ============================================================================
# Utility Fixtures
# ============================================================================

@pytest.fixture
def assert_eventually():
    """
    Utility fixture for asserting conditions that may take time to become true.
    Useful for async operations that need polling.
    """
    async def _assert_eventually(condition, timeout=5.0, interval=0.1, message="Condition not met"):
        import time
        start = time.time()
        while time.time() - start < timeout:
            if await condition() if asyncio.iscoroutinefunction(condition) else condition():
                return True
            await asyncio.sleep(interval)
        raise AssertionError(message)

    return _assert_eventually

