"""
Integration tests for MongoDB operations.

These tests verify that MongoDB operations work correctly with a real MongoDB instance.
Prerequisites: docker-compose -f docker-compose.test.yml up -d

Run with: poetry run pytest tests/integration/database/test_mongodb.py -v -m integration
"""

import pytest
from datetime import datetime, timezone
import uuid

pytestmark = [pytest.mark.integration]


class TestMongoDBBasicOperations:
    """Test basic MongoDB CRUD operations."""

    def test_insert_and_find_document(self, mongodb_collection):
        """Test inserting and finding a document."""
        doc = {
            "name": "test_document",
            "value": 42,
            "nested": {"key": "value"},
            "created_at": datetime.now(timezone.utc),
        }
        
        # Insert
        result = mongodb_collection.insert_one(doc)
        assert result.inserted_id is not None
        
        # Find
        found = mongodb_collection.find_one({"name": "test_document"})
        assert found is not None
        assert found["value"] == 42
        assert found["nested"]["key"] == "value"

    def test_insert_many_documents(self, mongodb_collection):
        """Test inserting multiple documents."""
        docs = [
            {"index": i, "name": f"doc_{i}"}
            for i in range(10)
        ]
        
        result = mongodb_collection.insert_many(docs)
        assert len(result.inserted_ids) == 10
        
        # Verify count
        count = mongodb_collection.count_documents({})
        assert count == 10

    def test_update_document(self, mongodb_collection):
        """Test updating a document."""
        # Insert
        mongodb_collection.insert_one({"name": "to_update", "value": 1})
        
        # Update
        result = mongodb_collection.update_one(
            {"name": "to_update"},
            {"$set": {"value": 100, "updated": True}}
        )
        
        assert result.modified_count == 1
        
        # Verify
        doc = mongodb_collection.find_one({"name": "to_update"})
        assert doc["value"] == 100
        assert doc["updated"] is True

    def test_delete_document(self, mongodb_collection):
        """Test deleting a document."""
        # Insert
        mongodb_collection.insert_one({"name": "to_delete"})
        
        # Verify exists
        assert mongodb_collection.find_one({"name": "to_delete"}) is not None
        
        # Delete
        result = mongodb_collection.delete_one({"name": "to_delete"})
        assert result.deleted_count == 1
        
        # Verify gone
        assert mongodb_collection.find_one({"name": "to_delete"}) is None

    def test_find_with_query(self, mongodb_collection):
        """Test finding documents with query filters."""
        # Insert test data
        docs = [
            {"category": "A", "value": 10},
            {"category": "A", "value": 20},
            {"category": "B", "value": 30},
            {"category": "B", "value": 40},
        ]
        mongodb_collection.insert_many(docs)
        
        # Find by category
        results = list(mongodb_collection.find({"category": "A"}))
        assert len(results) == 2
        
        # Find with comparison
        results = list(mongodb_collection.find({"value": {"$gt": 25}}))
        assert len(results) == 2

    def test_find_with_projection(self, mongodb_collection):
        """Test finding documents with field projection."""
        mongodb_collection.insert_one({
            "name": "test",
            "secret": "hidden",
            "public": "visible"
        })
        
        # Find with projection (exclude secret)
        doc = mongodb_collection.find_one(
            {"name": "test"},
            {"secret": 0}
        )
        
        assert "name" in doc
        assert "public" in doc
        assert "secret" not in doc

    def test_find_with_sort(self, mongodb_collection):
        """Test finding documents with sorting."""
        docs = [
            {"name": "c", "order": 3},
            {"name": "a", "order": 1},
            {"name": "b", "order": 2},
        ]
        mongodb_collection.insert_many(docs)
        
        # Sort ascending
        results = list(mongodb_collection.find().sort("order", 1))
        assert [r["name"] for r in results] == ["a", "b", "c"]
        
        # Sort descending
        results = list(mongodb_collection.find().sort("order", -1))
        assert [r["name"] for r in results] == ["c", "b", "a"]

    def test_find_with_limit_and_skip(self, mongodb_collection):
        """Test pagination with limit and skip."""
        docs = [{"index": i} for i in range(20)]
        mongodb_collection.insert_many(docs)
        
        # Get page 1 (first 5)
        page1 = list(mongodb_collection.find().sort("index", 1).limit(5))
        assert len(page1) == 5
        assert page1[0]["index"] == 0
        
        # Get page 2 (skip 5, take 5)
        page2 = list(mongodb_collection.find().sort("index", 1).skip(5).limit(5))
        assert len(page2) == 5
        assert page2[0]["index"] == 5

    def test_count_documents(self, mongodb_collection):
        """Test counting documents."""
        docs = [
            {"status": "active"},
            {"status": "active"},
            {"status": "inactive"},
        ]
        mongodb_collection.insert_many(docs)

        # Count all
        total = mongodb_collection.count_documents({})
        assert total == 3

        # Count with filter
        active = mongodb_collection.count_documents({"status": "active"})
        assert active == 2


class TestMongoDBIndexOperations:
    """Test MongoDB index operations."""

    def test_create_index(self, mongodb_collection):
        """Test creating an index."""
        # Create index
        index_name = mongodb_collection.create_index("name")

        assert index_name is not None

        # Verify index exists
        indexes = list(mongodb_collection.list_indexes())
        index_names = [idx["name"] for idx in indexes]
        assert index_name in index_names

    def test_create_unique_index(self, mongodb_collection):
        """Test creating a unique index."""
        mongodb_collection.create_index("unique_field", unique=True)

        # Insert first document
        mongodb_collection.insert_one({"unique_field": "value1"})

        # Try to insert duplicate - should fail
        with pytest.raises(Exception):  # DuplicateKeyError
            mongodb_collection.insert_one({"unique_field": "value1"})

    def test_create_compound_index(self, mongodb_collection):
        """Test creating a compound index."""
        index_name = mongodb_collection.create_index([
            ("field1", 1),
            ("field2", -1)
        ])

        assert index_name is not None


class TestMongoDBSessionManager:
    """Test MongoDBSessionManager with real MongoDB."""

    @pytest.fixture
    def session_manager(self, integration_config):
        """Create a session manager for testing."""
        from app.utils.mongodb_session_manager import MongoDBSessionManager

        manager = MongoDBSessionManager(
            mongo_uri=integration_config["mongodb_url"],
            db_name=integration_config["mongodb_db"],
            collection_name="test_sessions"
        )

        yield manager

        # Cleanup
        manager.clear_all_sessions()
        manager.close()

    def test_create_session(self, session_manager):
        """Test creating a new session."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"

        session = session_manager.create_session(session_id)

        assert session["session_id"] == session_id
        assert session["messages"] == []
        assert "created_at" in session
        assert "updated_at" in session

    def test_create_session_with_messages(self, session_manager):
        """Test creating a session with initial messages."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"
        messages = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"},
        ]

        session = session_manager.create_session(session_id, messages=messages)

        assert len(session["messages"]) == 2
        assert session["messages"][0]["role"] == "user"

    def test_create_duplicate_session_fails(self, session_manager):
        """Test that creating a duplicate session raises an error."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"

        session_manager.create_session(session_id)

        with pytest.raises(ValueError, match="already exists"):
            session_manager.create_session(session_id)

    def test_get_session(self, session_manager):
        """Test retrieving a session."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"
        session_manager.create_session(session_id, metadata={"key": "value"})

        session = session_manager.get_session(session_id)

        assert session is not None
        assert session["session_id"] == session_id
        assert session["metadata"]["key"] == "value"

    def test_get_nonexistent_session(self, session_manager):
        """Test retrieving a session that doesn't exist."""
        session = session_manager.get_session("nonexistent-session")
        assert session is None

    def test_update_session(self, session_manager):
        """Test updating a session."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"
        session_manager.create_session(session_id)

        new_messages = [{"role": "user", "content": "Updated"}]
        updated = session_manager.update_session(session_id, messages=new_messages)

        assert updated is not None
        assert len(updated["messages"]) == 1
        assert updated["messages"][0]["content"] == "Updated"

    def test_add_message(self, session_manager):
        """Test adding a message to a session."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"
        session_manager.create_session(session_id)

        session_manager.add_message(session_id, {"role": "user", "content": "First"})
        session_manager.add_message(session_id, {"role": "assistant", "content": "Second"})

        messages = session_manager.get_messages(session_id)

        assert len(messages) == 2
        assert messages[0]["content"] == "First"
        assert messages[1]["content"] == "Second"

    def test_delete_session(self, session_manager):
        """Test deleting a session."""
        session_id = f"test-session-{uuid.uuid4().hex[:8]}"
        session_manager.create_session(session_id)

        # Verify exists
        assert session_manager.get_session(session_id) is not None

        # Delete
        result = session_manager.delete_session(session_id)
        assert result is True

        # Verify gone
        assert session_manager.get_session(session_id) is None

    def test_list_sessions(self, session_manager):
        """Test listing sessions with pagination."""
        # Create multiple sessions
        for i in range(5):
            session_manager.create_session(f"list-test-{i}")

        # List all
        sessions = session_manager.list_sessions()
        assert len(sessions) == 5

        # List with limit
        sessions = session_manager.list_sessions(limit=3)
        assert len(sessions) == 3

        # List with skip
        sessions = session_manager.list_sessions(skip=2, limit=10)
        assert len(sessions) == 3

