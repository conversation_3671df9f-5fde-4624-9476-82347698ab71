"""
Integration tests for Redis Streams.

These tests verify that Redis Streams operations work correctly with a real Redis instance.
Prerequisites: docker-compose -f docker-compose.test.yml up -d

Run with: poetry run pytest tests/integration/messaging/test_redis_streams.py -v -m integration
"""

import pytest
import asyncio
import time
from unittest.mock import patch

pytestmark = [pytest.mark.integration, pytest.mark.asyncio]


class TestRedisStreamsBasicOperations:
    """Test basic Redis Streams operations."""

    async def test_xadd_and_xread(self, app_redis_client):
        """Test adding and reading messages from a stream."""
        stream = "test:stream:basic"
        
        # Add messages
        msg_id1 = await app_redis_client.xadd(stream, {"field1": "value1", "count": 1})
        msg_id2 = await app_redis_client.xadd(stream, {"field1": "value2", "count": 2})
        
        assert msg_id1 is not None
        assert msg_id2 is not None
        
        # Read messages
        result = await app_redis_client.xread({stream: "0"}, count=10)
        
        assert stream in result
        assert len(result[stream]) == 2
        
        # Verify message content
        _, fields1 = result[stream][0]
        _, fields2 = result[stream][1]
        
        assert fields1["field1"] == "value1"
        assert fields1["count"] == 1
        assert fields2["field1"] == "value2"
        assert fields2["count"] == 2

    async def test_xadd_with_maxlen(self, app_redis_client):
        """Test adding messages with maxlen limit using xtrim for exact trimming."""
        import uuid
        stream = f"test:stream:maxlen:{uuid.uuid4().hex[:8]}"

        # Add messages
        for i in range(20):
            await app_redis_client.xadd(stream, {"index": i})

        # Verify we have 20 messages
        length = await app_redis_client.xlen(stream)
        assert length == 20

        # Trim to exact 5 messages
        await app_redis_client.xtrim(stream, maxlen=5, approximate=False)

        # Verify trimming worked
        length = await app_redis_client.xlen(stream)
        assert length == 5

    async def test_xlen(self, app_redis_client):
        """Test getting stream length."""
        stream = "test:stream:len"
        
        # Empty stream
        length = await app_redis_client.xlen(stream)
        assert length == 0
        
        # Add messages
        await app_redis_client.xadd(stream, {"msg": "1"})
        await app_redis_client.xadd(stream, {"msg": "2"})
        await app_redis_client.xadd(stream, {"msg": "3"})
        
        length = await app_redis_client.xlen(stream)
        assert length == 3

    async def test_xtrim(self, app_redis_client):
        """Test trimming a stream."""
        stream = "test:stream:trim"
        
        # Add messages
        for i in range(10):
            await app_redis_client.xadd(stream, {"index": i})
        
        # Trim to 3 messages
        removed = await app_redis_client.xtrim(stream, maxlen=3, approximate=False)
        
        assert removed == 7
        
        length = await app_redis_client.xlen(stream)
        assert length == 3


class TestRedisStreamsConsumerGroups:
    """Test Redis Streams consumer group operations."""

    async def test_create_consumer_group(self, app_redis_client):
        """Test creating a consumer group."""
        stream = "test:stream:group"
        group = "test-group"
        
        # Create stream first
        await app_redis_client.xadd(stream, {"init": "true"})
        
        # Create consumer group
        await app_redis_client.xgroup_create(stream, group, id="0")
        
        # Verify group exists
        groups = await app_redis_client.xinfo_groups(stream)
        assert len(groups) == 1
        assert groups[0]["name"] == group

    async def test_create_consumer_group_mkstream(self, app_redis_client):
        """Test creating a consumer group with mkstream."""
        stream = "test:stream:mkstream"
        group = "test-group-mkstream"
        
        # Create group with mkstream (stream doesn't exist)
        await app_redis_client.xgroup_create(stream, group, id="0", mkstream=True)
        
        # Verify stream and group exist
        groups = await app_redis_client.xinfo_groups(stream)
        assert len(groups) == 1

    async def test_xreadgroup(self, app_redis_client):
        """Test reading messages with consumer groups."""
        stream = "test:stream:readgroup"
        group = "test-group"
        consumer = "consumer-1"
        
        # Create stream and group
        await app_redis_client.xadd(stream, {"msg": "1"})
        await app_redis_client.xadd(stream, {"msg": "2"})
        await app_redis_client.xgroup_create(stream, group, id="0")
        
        # Read messages
        result = await app_redis_client.xreadgroup(
            group, consumer, {stream: ">"}, count=10
        )
        
        assert stream in result
        assert len(result[stream]) == 2

    async def test_xack(self, app_redis_client):
        """Test acknowledging messages."""
        stream = "test:stream:ack"
        group = "test-group"
        consumer = "consumer-1"
        
        # Setup
        msg_id = await app_redis_client.xadd(stream, {"msg": "to-ack"})
        await app_redis_client.xgroup_create(stream, group, id="0")
        
        # Read message
        await app_redis_client.xreadgroup(group, consumer, {stream: ">"}, count=1)
        
        # Acknowledge
        acked = await app_redis_client.xack(stream, group, msg_id)
        assert acked == 1

    async def test_xgroup_destroy(self, app_redis_client):
        """Test destroying a consumer group."""
        stream = "test:stream:destroy"
        group = "test-group"

        # Create stream and group
        await app_redis_client.xadd(stream, {"init": "true"})
        await app_redis_client.xgroup_create(stream, group, id="0")

        # Verify group exists
        groups = await app_redis_client.xinfo_groups(stream)
        assert len(groups) == 1

        # Destroy group
        await app_redis_client.xgroup_destroy(stream, group)

        # Verify group is gone
        groups = await app_redis_client.xinfo_groups(stream)
        assert len(groups) == 0


class TestRedisStreamsPendingMessages:
    """Test pending message operations."""

    async def test_xpending_range(self, app_redis_client):
        """Test getting pending messages."""
        stream = "test:stream:pending"
        group = "test-group"
        consumer = "consumer-1"

        # Setup
        await app_redis_client.xadd(stream, {"msg": "pending-1"})
        await app_redis_client.xadd(stream, {"msg": "pending-2"})
        await app_redis_client.xgroup_create(stream, group, id="0")

        # Read but don't acknowledge
        await app_redis_client.xreadgroup(group, consumer, {stream: ">"}, count=10)

        # Get pending messages
        pending = await app_redis_client.xpending_range(stream, group, count=10)

        assert len(pending) == 2

    async def test_xclaim(self, app_redis_client):
        """Test claiming pending messages."""
        stream = "test:stream:claim"
        group = "test-group"
        consumer1 = "consumer-1"
        consumer2 = "consumer-2"

        # Setup
        msg_id = await app_redis_client.xadd(stream, {"msg": "to-claim"})
        await app_redis_client.xgroup_create(stream, group, id="0")

        # Consumer 1 reads but doesn't ack
        await app_redis_client.xreadgroup(group, consumer1, {stream: ">"}, count=1)

        # Wait a bit for idle time
        await asyncio.sleep(0.1)

        # Consumer 2 claims the message using the underlying client directly
        # (the wrapper method has issues with single message_id)
        result = await app_redis_client.client.xclaim(
            stream, group, consumer2, 0, [msg_id]  # 0ms min idle time
        )

        assert len(result) == 1
        assert result[0][0] == msg_id


class TestRedisStreamsInfo:
    """Test stream info operations."""

    async def test_xinfo_stream(self, app_redis_client):
        """Test getting stream info."""
        stream = "test:stream:info"

        # Add messages
        await app_redis_client.xadd(stream, {"msg": "1"})
        await app_redis_client.xadd(stream, {"msg": "2"})

        # Get info
        info = await app_redis_client.xinfo_stream(stream)

        assert info["length"] == 2
        assert "first-entry" in info
        assert "last-entry" in info

    async def test_xinfo_groups(self, app_redis_client):
        """Test getting consumer group info."""
        stream = "test:stream:groups-info"
        group1 = "group-1"
        group2 = "group-2"

        # Setup
        await app_redis_client.xadd(stream, {"init": "true"})
        await app_redis_client.xgroup_create(stream, group1, id="0")
        await app_redis_client.xgroup_create(stream, group2, id="0")

        # Get groups info
        groups = await app_redis_client.xinfo_groups(stream)

        assert len(groups) == 2
        group_names = [g["name"] for g in groups]
        assert group1 in group_names
        assert group2 in group_names


class TestRedisStreamsManager:
    """Test RedisStreamsManager high-level operations."""

    async def test_manager_initialization(self, app_redis_streams_manager):
        """Test manager initializes correctly."""
        assert app_redis_streams_manager._initialized is True
        assert app_redis_streams_manager.redis_client is not None

    async def test_producer_send_message(self, app_redis_streams_manager):
        """Test producer can send messages."""
        stream = "test:manager:producer"

        msg_id = await app_redis_streams_manager.producer.send_message(
            stream, {"test": "message", "count": 42}
        )

        assert msg_id is not None

        # Verify message was sent
        length = await app_redis_streams_manager.redis_client.xlen(stream)
        assert length == 1

    async def test_get_stream_info(self, app_redis_streams_manager):
        """Test getting stream info through manager."""
        stream = "test:manager:info"

        # Add some messages
        await app_redis_streams_manager.producer.send_message(stream, {"msg": "1"})
        await app_redis_streams_manager.producer.send_message(stream, {"msg": "2"})

        # Get info
        info = await app_redis_streams_manager.get_stream_info(stream)

        assert info["length"] == 2

    async def test_dead_letter_stream(self, app_redis_streams_manager):
        """Test dead letter stream creation."""
        original_stream = "test:manager:original"

        # Create dead letter stream
        dl_stream = await app_redis_streams_manager.create_dead_letter_stream(original_stream)

        assert dl_stream == f"{original_stream}:dead_letter"

        # Verify it exists
        length = await app_redis_streams_manager.redis_client.xlen(dl_stream)
        assert length >= 1  # At least the initialization message

    async def test_move_to_dead_letter(self, app_redis_streams_manager):
        """Test moving a message to dead letter queue."""
        from app.services.redis_streams import StreamMessage

        original_stream = "test:manager:move-dl"

        # Create a message
        message = StreamMessage(
            id="test-id-123",
            fields={"original": "data", "important": "value"},
            stream=original_stream
        )

        # Move to dead letter
        await app_redis_streams_manager.move_to_dead_letter(
            original_stream, message, "test_error"
        )

        # Verify message is in dead letter stream
        dl_stream = f"{original_stream}:dead_letter"
        result = await app_redis_streams_manager.redis_client.xread({dl_stream: "0"})

        assert dl_stream in result
        # Should have at least 2 messages (init + moved message)
        assert len(result[dl_stream]) >= 2

