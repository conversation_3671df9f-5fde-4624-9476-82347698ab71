# Integration Test Dependencies
# Usage: docker-compose -f docker-compose.test.yml up -d
# Cleanup: docker-compose -f docker-compose.test.yml down -v

services:
  test-redis:
    image: redis:7-alpine
    container_name: agent-platform-test-redis
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - test-redis-data:/data

  test-mongodb:
    image: mongo:7.0
    container_name: agent-platform-test-mongodb
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: test_user
      MONGO_INITDB_ROOT_PASSWORD: test_password
      MONGO_INITDB_DATABASE: test_db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - test-mongodb-data:/data/db

  test-qdrant:
    image: qdrant/qdrant:v1.7.4
    container_name: agent-platform-test-qdrant
    ports:
      - "6334:6333"
      - "6335:6334"
    environment:
      QDRANT__SERVICE__GRPC_PORT: 6334
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/readyz"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - test-qdrant-data:/qdrant/storage

volumes:
  test-redis-data:
  test-mongodb-data:
  test-qdrant-data:

networks:
  default:
    name: agent-platform-test-network

