#!/usr/bin/env python3
"""
Test script for the LangGraph-based planner agent.

This script demonstrates how to use the planner agent to generate workflow plans
and validate tool availability using RAG search.
"""

import asyncio
import os

from langchain_core.messages import HumanMessage

from app.langgraph_workflow.graph.planner_agent_graph import (
    create_planner_graph,
    create_planner_graph_with_checkpointing,
    extract_plan_steps,
    extract_tool_verification,
    plan_workflow,
)
from app.langgraph_workflow.models.state import State
from app.langgraph_workflow.models.workflowGraph import WorkflowGraph


async def test_planner_agent():
    """Test the planner agent with a sample workflow request."""

    print("Testing LangGraph Planner Agent...")
    print("=" * 50)

    # Create a sample workflow graph
    workflow_graph = WorkflowGraph()

    # Create initial state
    initial_state = State(
        main_agent_messages=[
            HumanMessage(
                content="Generate a workflow which generate a short mystery story and send via gmail. make it generic so it can be send to anyone"
            )
        ],
        planner_messages=[],
        workflow_generation_messages=[],
        plan=None,
        feedback=None,
        workflow_graph=workflow_graph,
    )

    # Create the planner graph
    try:
        planner_graph = create_planner_graph()
        print("✓ Planner graph created successfully")
    except Exception as e:
        print(f"✗ Error creating planner graph: {e}")
        return

    # Run the planner
    try:
        print("\nRunning planner agent...")
        result = await planner_graph.ainvoke(initial_state)

        print("\n" + "=" * 50)
        print("PLANNER RESULT:")
        print("=" * 50)

        if result.get("plan"):
            print("Generated Plan:")
            print(result["plan"])
        else:
            print("No plan generated")

        if result.get("planner_messages"):
            print(f"\nPlanner Messages: {len(result['planner_messages'])} messages")
            for i, msg in enumerate(result["planner_messages"]):
                print(f"Message {i+1}: {msg.content[:100]}...")

        print("\n✓ Planner agent executed successfully")

    except Exception as e:
        print(f"✗ Error running planner agent: {e}")
        import traceback

        traceback.print_exc()


async def test_planner_with_feedback():
    """Test the planner agent with feedback and previous plan."""

    print("\n" + "=" * 50)
    print("Testing Planner Agent with Feedback...")
    print("=" * 50)

    # Create a sample workflow graph
    workflow_graph = WorkflowGraph()

    # Create state with previous plan and feedback
    initial_state = State(
        main_agent_messages=[
            HumanMessage(
                content="Generate a workflow which generate a short mystery story and send via gmail. make it generic so it can be send to anyone"
            )
        ],
        planner_messages=[],
        workflow_generation_messages=[],
        plan="Previous plan: 1. Generate story 2. Send email",
        feedback="The previous plan was too simple. Please add more detailed steps for story generation and email formatting.",
        workflow_graph=workflow_graph,
    )

    # Create the planner graph
    planner_graph = create_planner_graph()

    # Run the planner with feedback
    try:
        print("Running planner agent with feedback...")
        result = await planner_graph.ainvoke(initial_state)

        print("\n" + "=" * 50)
        print("PLANNER RESULT WITH FEEDBACK:")
        print("=" * 50)

        if result.get("plan"):
            print("Revised Plan:")
            print(result["plan"])

        print("\n✓ Planner agent with feedback executed successfully")

    except Exception as e:
        print(f"✗ Error running planner agent with feedback: {e}")
        import traceback

        traceback.print_exc()


async def test_planner_with_checkpointing():
    """Test the planner agent with MongoDB checkpointing (if available)."""

    print("\n" + "=" * 50)
    print("Testing Planner Agent with Checkpointing...")
    print("=" * 50)

    mongo_uri = os.getenv("MONGO_DB_URL")
    if not mongo_uri:
        print("⚠ MongoDB URI not found, skipping checkpointing test")
        return

    try:
        # Create planner graph with checkpointing
        planner_graph = create_planner_graph_with_checkpointing(mongo_uri)
        print("✓ Planner graph with checkpointing created successfully")

        # Create initial state
        workflow_graph = WorkflowGraph()
        initial_state = State(
            main_agent_messages=[
                HumanMessage(content="Create a simple data processing workflow")
            ],
            planner_messages=[],
            workflow_generation_messages=[],
            plan=None,
            feedback=None,
            workflow_graph=workflow_graph,
        )

        # Run with checkpointing
        config = {"configurable": {"thread_id": "test_thread_123"}}
        result = await planner_graph.ainvoke(initial_state, config=config)

        print("✓ Planner agent with checkpointing executed successfully")

    except Exception as e:
        print(f"⚠ Checkpointing test failed: {e}")


async def main():
    """Main test function."""
    print("LangGraph Planner Agent Test Suite")
    print("=" * 50)

    # Run basic planner test
    await test_planner_agent()

    # Run planner test with feedback
    await test_planner_with_feedback()

    # Run planner test with checkpointing
    await test_planner_with_checkpointing()

    print("\n" + "=" * 50)
    print("All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
