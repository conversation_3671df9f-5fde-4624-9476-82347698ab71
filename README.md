# Agent Platform V2

A scalable, production-ready AI agent platform built with Python that provides intelligent conversational agents with tool capabilities, memory management, knowledge base integration, and real-time streaming responses.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Core Components](#core-components)
- [Agent System](#agent-system)
- [Services Layer](#services-layer)
- [Tools & Capabilities](#tools--capabilities)
- [Memory & State Management](#memory--state-management)
- [Observability](#observability)
- [Deployment](#deployment)
- [Getting Started](#getting-started)
- [Configuration](#configuration)

---

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                              AGENT PLATFORM V2                                   │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                  │
│  ┌────────────────────────────────────────────────────────────────────────────┐ │
│  │                         ENTRY POINT (app/main.py)                          │ │
│  │  • Application initialization & service orchestration                       │ │
│  │  • Background scheduler (vector DB updates)                                 │ │
│  │  • Telemetry & metrics setup                                                │ │
│  └────────────────────────────────────────────────────────────────────────────┘ │
│                                      │                                           │
│                                      ▼                                           │
│  ┌────────────────────────────────────────────────────────────────────────────┐ │
│  │                     REDIS STREAMS LISTENER                                  │ │
│  │  • Consumer groups for horizontal scaling                                   │ │
│  │  • Message routing (agent/workflow/memory/stop requests)                    │ │
│  │  • Dead letter queue & retry logic                                          │ │
│  └────────────────────────────────────────────────────────────────────────────┘ │
│                                      │                                           │
│         ┌────────────────────────────┼────────────────────────────┐             │
│         ▼                            ▼                            ▼             │
│  ┌──────────────┐           ┌──────────────┐           ┌──────────────┐         │
│  │ Agent Chat   │           │ Workflow Gen │           │ Workflow Chat│         │
│  │ (run_agent)  │           │  (LangGraph) │           │  (LangGraph) │         │
│  └──────────────┘           └──────────────┘           └──────────────┘         │
│         │                                                                        │
│         ▼                                                                        │
│  ┌────────────────────────────────────────────────────────────────────────────┐ │
│  │                         DEEP AGENT SYSTEM                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │  Supervisor │  │  Subagents  │  │    Tools    │  │ Middleware  │        │ │
│  │  │   Agent     │◄─┤ (Knowledge, │◄─┤ (MCP, Web,  │  │(Summarize,  │        │ │
│  │  │             │  │  Web, MCP)  │  │  Memory...) │  │ Cache...)   │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  └────────────────────────────────────────────────────────────────────────────┘ │
│                                      │                                           │
│         ┌────────────────────────────┼────────────────────────────┐             │
│         ▼                            ▼                            ▼             │
│  ┌──────────────┐           ┌──────────────┐           ┌──────────────┐         │
│  │   MongoDB    │           │    Redis     │           │    Qdrant    │         │
│  │(Checkpoints) │           │(State/Streams│           │   (Memory)   │         │
│  └──────────────┘           └──────────────┘           └──────────────┘         │
│                                                                                  │
│  ┌────────────────────────────────────────────────────────────────────────────┐ │
│  │                         EXTERNAL INTEGRATIONS                               │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │ AI Gateway  │  │  MCP Gateway│  │ Knowledge   │  │   SigNoz    │        │ │
│  │  │(OpenRouter) │  │   (Tools)   │  │    Base     │  │   (OTEL)    │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  └────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                  │
└─────────────────────────────────────────────────────────────────────────────────┘
```

---

## Core Components

### Directory Structure

```
agent-platform-v2/
├── app/
│   ├── main.py                 # Application entry point
│   ├── agent/                  # Agent system
│   │   ├── config/             # Model & memory configuration
│   │   ├── model.py            # Chat model factory
│   │   ├── run_agent.py        # Agent execution orchestrator
│   │   ├── state.py            # State definitions (Todo)
│   │   ├── subagents/          # Specialized subagents
│   │   ├── system_prompts/     # Agent prompts & instructions
│   │   ├── tools/              # Agent tools
│   │   └── utils/              # Agent utilities
│   ├── services/               # External service integrations
│   │   ├── redis_streams.py    # Redis Streams producer/consumer
│   │   ├── redis_listener.py   # Main event listener
│   │   ├── mongodb_client.py   # MongoDB connection & checkpointer
│   │   ├── http_client.py      # HTTP client pool
│   │   ├── agent_fetch.py      # Agent configuration service
│   │   └── stream_formatter.py # LangGraph event formatter
│   ├── shared/
│   │   └── config/             # Configuration modules
│   │       ├── base.py         # Settings & environment config
│   │       ├── constants.py    # Enums & constants
│   │       ├── logging_config.py # Structured logging
│   │       └── telemetry.py    # OpenTelemetry setup
│   ├── utils/                  # Utility modules
│   │   ├── tracing.py          # Distributed tracing
│   │   ├── metrics.py          # Metrics collection
│   │   ├── exceptions.py       # Exception tracking
│   │   ├── mcp_client.py       # MCP tool executor
│   │   └── agent_redis_store.py # Redis state storage
│   ├── helper/                 # Helper functions
│   ├── workflow_chat/          # Workflow chat agent
│   └── workflow_generation_graph/ # Workflow generation
├── tests/                      # Test suite
├── helm/                       # Kubernetes Helm charts
├── Dockerfile                  # Container definition
└── pyproject.toml              # Dependencies & configuration
```

---

## Agent System

### Agent Types

#### 1. Global Agent
The default agent for general-purpose conversations with access to all platform capabilities.

```python
# Created via deepagent_utils.create_deep_agent()
agent = await create_deep_agent(
    provider="openai",
    model_name="gpt-4o",
    use_knowledge=True,
    use_search=True,
    use_memory=True,
    is_global=True,  # Global agent mode
    ...
)
```

#### 2. Custom Agents
Organization-specific agents with custom configurations, MCPs, and workflows.

```python
agent = await create_deep_agent(
    agent_id="custom-agent-123",  # Fetches config from API
    is_global=False,              # Custom agent mode
    ...
)
```

### Agent Architecture (DeepAgent)

```
┌─────────────────────────────────────────────────────────────┐
│                    RUH DEEP AGENT                           │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                  SUPERVISOR AGENT                    │   │
│  │  • Dynamic system prompt (global/custom)             │   │
│  │  • Tool orchestration                                │   │
│  │  • Subagent delegation                               │   │
│  └─────────────────────────────────────────────────────┘   │
│                          │                                  │
│         ┌────────────────┼────────────────┐                │
│         ▼                ▼                ▼                │
│  ┌────────────┐   ┌────────────┐   ┌────────────┐         │
│  │ Knowledge  │   │ Web Search │   │    MCP     │         │
│  │  Subagent  │   │  Subagent  │   │  Subagent  │         │
│  └────────────┘   └────────────┘   └────────────┘         │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                   MIDDLEWARE STACK                   │   │
│  │  • SummarizationMiddleware (100k token limit)        │   │
│  │  • SubAgentMiddleware (delegation)                   │   │
│  │  • AnthropicPromptCachingMiddleware                  │   │
│  │  • PatchToolCallsMiddleware                          │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Subagents

| Subagent | Purpose | Tools |
|----------|---------|-------|
| **Knowledge Base** | Search organization knowledge | `search_by_source`, `resolve_identity` |
| **Web Search** | Real-time web information | `web_search` (Tavily) |
| **MCP** | Execute MCP protocol tools | `execute_mcp` |
| **Workflow** | Execute workflow automations | `workflow_execution` (disabled) |

---

## Services Layer

### Redis Streams Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    REDIS STREAMS                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  REQUEST STREAMS (Consumer Groups)                          │
│  ┌────────────────────────────────────────────────────┐    │
│  │ {env}:agent:chat:requests     → Agent requests      │    │
│  │ {env}:workflow:requests       → Workflow generation │    │
│  │ {env}:workflow:chat:requests  → Workflow chat       │    │
│  │ {env}:memory:requests         → Memory storage      │    │
│  │ {env}:agent:stop:requests     → Stop signals        │    │
│  └────────────────────────────────────────────────────┘    │
│                                                             │
│  RESPONSE STREAMS (Per conversation)                        │
│  ┌────────────────────────────────────────────────────┐    │
│  │ {env}:agent:chat:responses:{conv_id}-{req_id}       │    │
│  │ {env}:workflow:responses:{req_id}                   │    │
│  └────────────────────────────────────────────────────┘    │
│                                                             │
│  Consumer Group: agent-processors                           │
│  • Unique consumer per instance (horizontal scaling)        │
│  • Pending message handling                                 │
│  • Dead letter queue for failed messages                    │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Stream Event Types

```python
class EventType(str, Enum):
    STREAM_START = "stream_start"     # Stream begins
    STREAM_END = "stream_end"         # Stream completes
    MESSAGE_START = "message_start"   # LLM response starts
    MESSAGE = "message"               # Text chunk
    MESSAGE_END = "message_end"       # LLM response ends
    TOOL_START = "tool_start"         # Tool execution starts
    TOOL_END = "tool_end"             # Tool execution ends
    TOOL_STREAM = "tool_stream"       # Tool streaming output
    THINKING_START = "thinking_start" # Reasoning starts
    THINKING = "thinking"             # Reasoning chunk
    THINKING_END = "thinking_end"     # Reasoning ends
```

### MongoDB Integration

- **Checkpointing**: LangGraph state persistence via `MongoDBSaver`
- **Conversation History**: Automatic checkpoint storage per thread_id
- **Summary Storage**: Conversation summaries for context management

---

## Tools & Capabilities

### Core Tools

| Tool | Description |
|------|-------------|
| `write_todos` | Create/manage task lists for complex workflows |
| `read_todos` | Retrieve current task status |
| `read_file` | Read file contents from attachments |
| `create_file` | Generate files (CSV, PDF, TXT, MD) |
| `mcp_search` | Search available MCP tools (global only) |
| `add_memory` | Store information in long-term memory |
| `get_memory` | Retrieve relevant memories |

### MCP (Model Context Protocol) Integration

```
┌─────────────────────────────────────────────────────────────┐
│                    MCP INTEGRATION                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Agent/User    │    │   MCP Gateway   │                │
│  │                 │───▶│                 │                │
│  └─────────────────┘    └────────┬────────┘                │
│                                  │                          │
│         ┌────────────────────────┼────────────────────────┐│
│         ▼                        ▼                        ▼││
│  ┌────────────┐         ┌────────────┐         ┌────────────┐
│  │ Gmail MCP  │         │ Jira MCP   │         │ Slack MCP  │
│  └────────────┘         └────────────┘         └────────────┘
│                                                             │
│  Tool Execution Flow:                                       │
│  1. Agent calls execute_mcp(mcp_slug, tool_name, params)    │
│  2. MCPClient connects to gateway via streamable HTTP       │
│  3. Tool result returned to agent context                   │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Knowledge Base Integration

Searches organizational data from multiple sources:
- Google Drive
- Gmail
- Confluence
- Jira
- GitHub
- Google Calendar

---

## Memory & State Management

### Long-Term Memory (Mem0 + Qdrant)

```
┌─────────────────────────────────────────────────────────────┐
│                    MEMORY SYSTEM                            │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                      Mem0                            │   │
│  │  • Memory extraction from conversations              │   │
│  │  • Relevance scoring & retrieval                     │   │
│  │  • OpenAI embeddings (text-embedding-3-small)        │   │
│  └─────────────────────────────────────────────────────┘   │
│                          │                                  │
│                          ▼                                  │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                     Qdrant                           │   │
│  │  • Vector storage for memory embeddings              │   │
│  │  • Fast similarity search                            │   │
│  │  • Collection: langgraph_memory                      │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Memory Metadata:                                           │
│  • user_id, organisation_id, agent_id, conversation_id      │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Redis State Storage

For horizontal scalability, transient state is stored in Redis:

```python
# Sources, approval data stored per conversation
await store_sources(conversation_id, sources)
await store_approval_data(conversation_id, approval_data)

# Stop signal handling
await check_stop_signal(conversation_id, redis_client)
```

### Conversation Summarization

Automatic summarization when token count exceeds threshold:
- **Trigger**: 100,000 tokens
- **Keep**: Last 6 messages
- **Provider**: Configurable (default: Google Gemini)

---

## Observability

### OpenTelemetry Integration (SigNoz)

```
┌─────────────────────────────────────────────────────────────┐
│                    OBSERVABILITY STACK                      │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                     TRACES                           │   │
│  │  • Distributed tracing across all operations         │   │
│  │  • Agent execution spans                             │   │
│  │  • Tool call instrumentation                         │   │
│  │  • External API tracking (HTTP, MongoDB)             │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                     METRICS                          │   │
│  │  • agent.executions, agent.execution.duration        │   │
│  │  • tool.calls, tool.execution.duration               │   │
│  │  • external_api.calls, external_api.duration         │   │
│  │  • memory.operations, workflow.executions            │   │
│  │  • exceptions.occurred                               │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                      LOGS                            │   │
│  │  • JSON structured logging                           │   │
│  │  • Automatic trace correlation (trace_id, span_id)   │   │
│  │  • Log levels: DEBUG, INFO, WARNING, ERROR           │   │
│  │  • Rotating file handlers per component              │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  Instrumented Components:                                   │
│  • HTTPX/Requests (auto)                                    │
│  • PyMongo (auto)                                           │
│  • Custom operations via trace_operation()                  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Exception Tracking

```python
exception_tracker = get_exception_tracker()
exception_tracker.track_exception(
    e,
    severity="error",
    attributes={
        "component": "agent_stream",
        "user_id": user_id,
        "conversation_id": conversation_id,
    },
)
```

---

## Deployment

### Docker

```dockerfile
FROM python:3.11-slim

# Poetry-based dependency management
RUN poetry install --no-interaction --no-ansi --no-root --without dev

# Application runs as server
CMD ["poetry", "run", "python", "-m", "app.main", "--mode", "server"]
```

### Kubernetes (Helm)

```
helm/
├── values.yaml      # Base configuration
├── values.dev.yaml  # Development environment
├── values.qa.yaml   # QA environment
└── values.main.yaml # Production environment
```

### Environment-Based Stream Names

All Redis streams are prefixed with environment:
- `dev:agent:chat:requests`
- `qa:agent:chat:requests`
- `main:agent:chat:requests`

---

## Getting Started

### Prerequisites

- Python 3.11+
- Poetry
- Redis
- MongoDB
- Qdrant

### Installation

```bash
# Install dependencies
poetry install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run the application
poetry run python -m app.main
```

### Running Tests

```bash
# Run all tests with coverage
poetry run pytest --cov=app --cov-report=html --cov-report=term-missing

# Run specific test file
poetry run pytest tests/test_file.py::test_function -v

# Quick test script
python scripts/test_global_agent.py
```

---

## Configuration

### Key Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `ENV` | Environment (dev/qa/main) | `dev` |
| `REDIS_HOST` | Redis server host | `localhost` |
| `REDIS_PORT` | Redis server port | `6379` |
| `MONGO_DB_URL` | MongoDB connection string | - |
| `OPENROUTER_API_KEY` | OpenRouter API key | - |
| `AI_GATEWAY_BASE_URL` | AI Gateway URL | `https://openrouter.ai/api/v1` |
| `QDRANT_HOST` | Qdrant server host | `localhost` |
| `QDRANT_PORT` | Qdrant server port | `6333` |
| `MCP_GATEWAY_URL` | MCP Gateway URL | - |
| `OTEL_ENABLED` | Enable OpenTelemetry | `false` |
| `SIGNOZ_ENDPOINT` | SigNoz OTLP endpoint | `http://localhost:4317` |

### Logging Configuration

```bash
# Log level (comma-separated for multiple)
LOG_LEVEL=DEBUG,INFO,ERROR

# JSON format for structured logging
LOG_FORMAT=json

# Logs directory
LOGS_DIR=logs
```

---

## Data Flow

```
┌──────────┐     ┌──────────────┐     ┌──────────────┐     ┌──────────────┐
│  Client  │────▶│ API Gateway  │────▶│ Redis Stream │────▶│Agent Platform│
│          │     │              │     │  (Requests)  │     │              │
└──────────┘     └──────────────┘     └──────────────┘     └──────┬───────┘
                                                                   │
     ┌─────────────────────────────────────────────────────────────┘
     ▼
┌──────────────────────────────────────────────────────────────────────┐
│                         AGENT EXECUTION                              │
│                                                                      │
│  1. Parse request (user_message, config, attachments)                │
│  2. Create/load agent (global or custom)                             │
│  3. Stream agent response (LangGraph astream_events)                 │
│  4. Format events (stream_formatter)                                 │
│  5. Publish to response stream                                       │
│  6. Update checkpoints & memory                                      │
│                                                                      │
└──────────────────────────────────────────────────────────────────────┘
     │
     ▼
┌──────────────┐     ┌──────────────┐     ┌──────────┐
│ Redis Stream │────▶│ API Gateway  │────▶│  Client  │
│ (Responses)  │     │    (SSE)     │     │          │
└──────────────┘     └──────────────┘     └──────────┘
```

---

## License

Private - All rights reserved.
