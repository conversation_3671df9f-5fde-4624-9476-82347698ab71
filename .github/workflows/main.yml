name: Build and Deploy Application

on:
  push:
    branches:
      - dev
      - qa
      - main
  workflow_dispatch:     
jobs:
  build:
    permissions:
      contents: read
      id-token: write  # Required for OIDC
    uses: ruh-ai/reusable-workflows-and-charts/.github/workflows/reusable-build.yaml@workflow/v1.3.1
    # with: # Variables: gcp_repository, gcp_artifact_registry_host, slack_channel
    secrets: inherit

  deploy:
    permissions:
      contents: read
      id-token: write  # Required for OIDC
    needs: build
    uses: ruh-ai/reusable-workflows-and-charts/.github/workflows/reusable-deploy-gke.yaml@workflow/v1.3.1
    # with: # Variables: namespace, values_file, chart_oci_path, chart_version, gcp_artifact_registry_host, slack_channel
    secrets: inherit